{"app-title": "Client Area", "menu": {"home": "Home", "billing": "Invoices", "profile": "Profile", "help": "Help", "notification": "Notifications", "products": "Products", "promotions": "Store"}, "general": {"loading": "Loading"}, "profile": {"name": "Profile", "title": "My Profile", "logout": "Logout", "logout-confirm": "Are you sure you want to log out?", "list": {"title": "List of {key}", "default": "Default {key}", "others": "Other {key}", "provisionContacts": "Contacts", "billingInfos": "Billing Profiles", "billingAddresses": "Billing Addresses", "shippingAddresses": "Shipping Addresses", "editAction": "Edit", "deleteAction": "Delete", "defaultAction": "Set as <PERSON><PERSON><PERSON>", "createAction": "Add {key}"}, "edit": {"edit": "Edit", "title": "Edit {key}", "provisionContacts": "Contact", "billingInfos": "Billing Profile", "billingAddresses": "Billing Address", "shippingAddresses": "Shipping Address", "save-address": "Save new address", "settings": {"preferredLanguage": "Change Language"}, "sending-data": "Sending data...", "loading-data": "Loading data...", "success": "Data has been successfully edited!", "error": "Oops! It seems there was an error! Please try again later.", "back-button": "Back to Profile Page", "isDefault": "<PERSON><PERSON><PERSON>"}, "create": {"create": "Create", "title": "Create {key}", "provisionContacts": "Contact", "billingInfos": "Billing Profile", "billingAddresses": "Billing Address", "shippingAddresses": "Shipping Address", "success": "Data has been successfully created!"}, "provisionContacts": {"profile-title": "Contact Details", "title-single": "Contact", "name": "Contact Name", "email": "Email", "phone": "Phone", "delete": "Are you sure you want to delete this contact?"}, "billingAddresses": {"title-single": "Billing Address", "actual-address": "Current Address", "new-address": "Enter New Address", "delete": "Are you sure you want to delete this billing address?"}, "billingInfos": {"profile-title": "Bank Details", "title-single": "Billing Profile", "name": "Billing Profile Name", "cccOwner": "Account Holder", "cccOwnerIdentification": "Holder's Document Number", "iban": "IBAN Number", "sendBill": "Invoice Format", "delete": "Are you sure you want to delete this billing profile?", "sendBillFormat": {"paper": "Paper", "email": "Email"}}, "shippingAddresses": {"title-single": "Shipping Address", "delete": "Are you sure you want to delete this shipping address?"}, "companyStructure": {"profile-title": "Administrative Data", "administratorDocumentNumber": "Administrator's Document Number", "administratorDocumentType": "Administrator's Document Type", "companyManagerFirstName": "Administrator's First Name", "companyManagerLastName": "Administrator's Last Name"}, "personalData": {"profile-title": "Personal Data", "completeName": "Full Name", "nationality": "Nationality", "documentNumber": "Document Number", "foundationDate": "Foundation Date", "personBirthdate": "Birthdate"}, "preferredLanguage": "Language", "settings": {"profile-title": "Settings", "title": "Settings", "preferredLanguage": "Language"}}, "home": {"name": "Home", "title": "Hello", "contracted-products": "Your Products", "last-invoice-available-title": "Your Last Invoice", "last-invoice-available": "Invoice Available", "help": "Help", "notifications": "Notifications", "actions": "Available Actions", "management": {"title": "Actions", "consumption": "Check Consumption", "roaming": "Roaming", "profile": "Edit Data", "help": "I Have an Issue"}}, "billing": {"name": "Invoices", "name-single": "Invoice", "title": "My Invoices", "no-invoice-available": "No Invoices Available", "invoice": {"total-month": "Total", "status-invoice-paid": "Paid", "status-invoice-pending": "Pending", "status-invoice-current-month": "In progress", "total-month-taxes-included": "Includes Taxes", "date-of-issue": "Issue Date", "date-of-payment": "Payment Date", "invoice-number": "Invoice Number", "button-view-invoice": "VIEW INVOICE", "invoice-history": "Invoice History", "invoice-config": "Bank Details", "invoice-price": "Invoice Price", "show-more": "Show More", "show-less": "Show Less", "download-invoice-title": "Download Invoice", "download-invoice-button": "Download", "invoice-for": "Invoice for:", "no-invoice": "No Invoice Available", "no-generate-invoice": "Could Not Generate PDF", "no-available-pdf": "PDF Not Available"}}, "help": {"name": "Help", "title": "Help and Support"}, "notifications": {"name": "Notifications", "title": "Notifications", "notifications-detail": {"title": "Notification Detail"}}, "products": {"name": "Products", "title": "My Products", "contracted-products": "Contracted Products", "current-rate": "Current Product Rate", "current-rate-details": "View Rate Details", "list-calls": "{key} calls", "list-calls-info": "View call breakdown", "no-available": "At this moment you don’t have any active product", "loading": "Loading product data", "consum-loading": "Loading product consumption", "no-data": "We currently have no data available for this product", "minuts": "Minutes", "start-date": "Start Date", "type": {"fiber": "Fiber", "landline": "Landline", "mobile": "Mobile", "tv": "Television", "digitalservice": "Digital Service", "refund": "Subscription"}, "fiber": {"address": "Installation Address:", "speed": "Fiber Speed", "speed-end": "speed"}, "landline": {"landline-to-landline": "National Landline to Landline Calls", "landline-to-mobile": "National Landline to Mobile Calls"}, "mobile": {"mobile-to-mobile": "National Mobile to Mobile Calls", "mobile-to-landline": "National Mobile to Landline Calls", "calls-to-landline": "Calls to Landlines", "calls-to-mobile": "Calls to Mobiles", "calls-national": "National calls", "data": "Data", "shared-data": "Shared", "shared-data-single": "Shared"}, "refund": {"data": "Data Subscription", "voice": "Voice Subscription"}, "roaming": {"name": "Roaming", "title": "Roaming information", "info": "Manage the roaming of this mobile", "status-roaming-actived": "Roaming is enabled", "status-roaming-desactived": "Roaming is disabled", "button-roaming-deactivate": "Deactivate roaming", "button-roaming-activate": "Activate roaming", "activating": "Activating...", "deactivating": "Deactivating...", "success-message": "The change was made successfully", "error-message": "Oops! An error occurred.", "confirm-activate-title": "Confirm roaming activation", "confirm-activate-message": "Are you sure you want to activate roaming? This will allow you to use your mobile abroad.", "confirm-deactivate-title": "Confirm roaming deactivation", "confirm-deactivate-message": "Are you sure you want to deactivate roaming? You won’t be able to use your mobile abroad.", "description-roaming-title": "What is Roaming? Roaming Rates and International Calls", "description-roaming-details": "To learn what roaming is, what it allows you to do, check the associated countries, and review roaming and international call rates, click the following link:", "description-roaming-price": "Learn more and check rates"}, "subscription": {"name": "Subscriptions", "info": "Subscribe Here to Your Subscriptions", "title": "Line Subscriptions", "manage-subscription": "Manage Subscriptions", "dataSubscriptions": "Data Subscriptions", "voiceSubscriptions": "Voice Subscriptions", "description-subscription-title": "Information About Subscriptions", "description-subscription-details": "You can subscribe to additional subscriptions for each of your lines if you need more data or minutes", "description-subscription-info": "How Does It Work?", "description-subscription-info-details": "You can subscribe to additional subscriptions for each of your lines if you need more data or minutes", "voice100Min": "100min Subscription", "data3Gb": "3GB Subscription"}, "services": {"name": "Services", "info": "Subscribe to More Services Here"}, "rate": {"name": "Rate Details", "characteristics": "Service features", "products": "Included Products", "price": "Price", "charges": "Charges and Penalties", "cost": "Cost", "iva": "/month (VAT included)", "final-price": "After {month} months, final product price {price}€/month (VAT included)", "error": "No Rate Information Available", "no-products": "No Products Associated with This Rate", "unlimited": "Unlimited Minutes", "expense": "Expense", "penalty": "Penalty"}, "call-details": {"name": "Call Details"}, "consumption": {"name": "Consumption", "see-all": "See All", "see-data": "Data", "see-calls": "Calls", "diagram-circle-text-inside": "Usage", "diagram-circle-consumption-calls": "Call Consumption", "diagram-circle-consumption-minutes-calls": "minutes per month", "diagram-circle-consumption-data": "Data Consumption", "diagram-circle-consumption-gb-data": "gigabytes per month", "diagram-chartBill-current-rate": "Current Rate", "diagram-chartBill-legend-calls": "Minutes", "diagram-chartBill-legend-calls-seconds": "Seconds", "diagram-chartBill-legend-calls-acronym": "<PERSON>.", "diagram-chartBill-legend-calls-seconds-acronym": "Sec.", "diagram-chartBill-legend-callsLandline": "Landline Minutes", "diagram-chartBill-legend-callsMobile": "Mobile Minutes", "diagram-chartBill-legend-alert": "Current month: only the total minutes are shown.", "diagram-chartBill-legend-data": "Gigabytes (GB)", "no-data": "This phone has no consumption", "error": "An Error Occurred. We Cannot Show Data. Please Try Again Later.", "unlimited": "Unlimited", "total-data": "Of {total} GB", "total-data-unlimited": "Unlimited GB", "total-used": "Total GB Usage", "available": "Available", "totals": "totals", "total-calls": "Total minutes", "total-calls-minutes": "Of {total} minutes", "total-calls-minutes-unlimited": "Of unlimited minutes"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "months-acronym": {"january-acronym": "Jan", "february-acronym": "Feb", "march-acronym": "Mar", "april-acronym": "Apr", "may-acronym": "May", "june-acronym": "Jun", "july-acronym": "Jul", "august-acronym": "Aug", "september-acronym": "Sep", "october-acronym": "Oct", "november-acronym": "Nov", "december-acronym": "Dec"}, "info-message": {"selected-product": "Your Selected Product Is", "notification-about-working-process": "We Want to Inform You That We Are Working to Offer You a Better Service on Our Website. Most Likely, in the Coming Days, the Content You Are Looking For Will Be Available in This Section of Our Site, So You Can Make All Queries and Downloads from Wherever You Are"}}, "promotions": {"name": "Promotions", "title": "Promotions for You"}, "management": {"title": "Actions"}, "incidence": {"name": "Incident", "title": "My Incidents", "info": "Do You Have a Problem With", "description-incidence-title": "Do You Have a Problem or Breakdown?", "button-incidence-communicate": "Report Incident or Breakdown", "text-description-incidence-1": "Or You Can Contact Us Free of Charge at", "text-description-incidence-2": "or at", "text-description-incidence-3": "24 Hours a Day, 365 Days a Year, and Also by Email"}, "preferredLanguage": {"es": "Spanish", "ca": "Catalan", "en": "English", "pt": "Portuguese", "fr": "French", "it": "Italian", "ru": "Russian", "va": "Valencian", "gl": "Galician", "zh": "Chinese", "null": "Catalan"}, "popup": {"yes": "Yes", "no": "No, Cancel"}, "under-construction": {"title": "Under Construction!", "message": "We Are Currently Working to Offer a Good Service. Please Come Back Soon."}, "login": {"login": "<PERSON><PERSON>", "signup": "Create Your Account", "signup-step": "Registration", "identify-yourself": "Identify Yourself", "identify": "Identification", "introduce-document": "Please Enter Your DNI, NIE, or CIF to Identify Yourself", "send": "Continue", "send-login": "Continue and Login", "mail-sent": "We Have Sent You an Email to Your Contact Email Address to Verify Your Data.", "verify-email": "If You Cannot Find the Email, Check Your Spam Folder.", "wait-email": "The email may take a few minutes to arrive.", "send-again": "I Did Not Receive the Email. Send Again", "user-registered": "Registration Cannot Be Completed. The Document Number Entered Is Already Registered or Is Not Valid.", "try-again": "Try Another Document Number", "social-email": "Enter Your Email", "repeat-social-mail": "Repeat Your Email", "social-email-info": "Register to Access the App and the Client Area.", "sending": "Sending", "connect": "Connect With", "where": "wherever you are", "verify": "Verify Your Identity", "logging": "Registering Client...", "info-signup": "Create an Account to Access Your Private Area and Manage Your Services, Check Your Invoices, and View Your Consumption.", "dni-example": "Ex. 12345678A", "already-client": "I Already Have an Account", "we-sorry": "Oops!", "go-back": "Go Back", "from": "from", "continue": "Continue With", "send-doc": "Enter Doc.", "verification": "Verification", "help": "Need Help? Contact Us at 1713", "success-title": "Registration completed!", "success": "Your account has been created successfully.", "success-login": "You can now log in.", "go-login": "Back to home"}, "errors": {"401": {"number": "401", "title": "Error 401", "message": "Oops! Your session has expired.", "submessage": "Please log in again.", "button-message": "Go back to home"}, "404": {"number": "404", "title": "Page not found", "message": "The page you are looking for does not exist. Please try again.", "button-message": "Go back to home"}}}