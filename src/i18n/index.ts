import { createI18n } from 'vue-i18n'
import en from '../i18n/locales/en.json'
import es from '../i18n/locales/es.json'
import ca from '../i18n/locales/ca.json'
import va from '../i18n/locales/va.json'
import gl from '../i18n/locales/gl.json'
import fr from '../i18n/locales/fr.json'
import it from '../i18n/locales/it.json'
import pt from '../i18n/locales/pt.json'
import ru from '../i18n/locales/ru.json'
import zh from '../i18n/locales/zh.json'

const i18nInstance = createI18n({
  locale: import.meta.env.VITE_DEFAULT_LOCALE,
  fallbackLocale: import.meta.env.VITE_FALLBACK_LOCALE,
  legacy: false,
  globalInjection: true,
  sync: false,
  messages: {
    en,
    es,
    ca,
    va,
    gl,
    fr,
    pt,
    it,
    ru,
    zh
  }
})
export default i18nInstance
export const i18n = i18nInstance.global
