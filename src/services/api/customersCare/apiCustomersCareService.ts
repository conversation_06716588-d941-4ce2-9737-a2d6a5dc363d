import axios from 'axios';
import {
  CONTENT_TYPE,
  APPLICATION_JSON_PATCH_JSON,
  API,
  BEARER,
  AUTHORIZATION,
  COMPANIES,
  B2C
} from '@/services/api/constants/apiConstants';
import {
  CUSTOMERS_CARE,
  PICKLISTS,
  CUSTOMER,
  ME,
  SERVICE,
  SU<PERSON><PERSON>IPTIONS,
  SERVICES,
  ROAMING,
  PICKLIST,
  LANGUAGES,
  PRODUCTS,
  PRODUCT,
  CDR,
  DETAILS,
  SAVE_SOCIAL_MAIL,
  SEND_MAIL
} from '@/services/api/customersCare/constants/customersCare.constants';
import type { IApiRes, IHeaders } from '@/services/api/interfaces';
import type { IApiCustomersCare, IPicklist } from './interfaces';
import { useAuthStore } from '@/stores/auth/index';
import type { ICustomerInfo } from '@/stores/customersCare/interfaces/customer-info.interface';
import type { IRoamingService } from '@/stores/customersCare/interfaces/roaming-service.interface';
import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';

function createCustomersCareHeader() {
  const authStore = useAuthStore();
  const token = authStore.accessToken;

  const customersCareHeaders: IHeaders = {
    headers: {
      [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON,
      [AUTHORIZATION]: `${BEARER}${token}`,
      'Access-Control-Allow-Origin': '*'
    }
  };
  return customersCareHeaders;
}

const apiCustomersCareService: IApiCustomersCare = {
  async getPicklists(language: string): Promise<IPicklist[]> {
    const url: string = `${import.meta.env.VITE_BASE_URL}/${B2C}/${CUSTOMERS_CARE}/${API}/${PICKLIST}/${LANGUAGES}/${language}/${PICKLISTS}`;
    const response: IApiRes<IPicklist[]> = await axios.get(url, createCustomersCareHeader());
    return response && response.data;
  },

  async getCustomerInfo(company: string): Promise<ICustomerInfo> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${B2C}/${CUSTOMERS_CARE}/${API}/${CUSTOMER}/${COMPANIES}/${company}/${ME}`;
      const response: IApiRes<any> = await axios.get(url, createCustomersCareHeader());
      return response.data;
    } catch (error: any) {
      return error.response.status;
    }
  },

  async updateCustomerInfo<T extends keyof ICustomerInfo>(
    company: string,
    property: T,
    newCustomerInfo: ICustomerInfo[T] | ICustomerInfo[T][]
  ): Promise<
    IApiRes<{
      item: ICustomerInfo[T] | ICustomerInfo[T][];
    }>
  > {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${B2C}/${CUSTOMERS_CARE}/${API}/${CUSTOMER}/${COMPANIES}/${company}/${ME}/${property.toString().toLowerCase()}`;
      const response: IApiRes<{
        item: ICustomerInfo[T];
      }> = await axios.put(url, newCustomerInfo, createCustomersCareHeader());
      return response;
    } catch (error: any) {
      return error.response;
    }
  },

  async getRoamingService(companyId: string, subscriptionId: string): Promise<any> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${B2C}/${CUSTOMERS_CARE}/${API}/${SERVICE}/${COMPANIES}/${companyId}/${SUBSCRIPTIONS}/${subscriptionId}/${SERVICES}/${ROAMING}`;
      const response: IApiRes<IRoamingService> = await axios.get(url, createCustomersCareHeader());
      return response;
    } catch (error: any) {
      return error.response;
    }
  },

  async activeRoamingService(
    companyId: string,
    subscriptionId: string,
    newStatus: boolean
  ): Promise<any> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${B2C}/${CUSTOMERS_CARE}/${API}/${SERVICE}/${COMPANIES}/${companyId}/${SUBSCRIPTIONS}/${subscriptionId}/${SERVICES}/${ROAMING}/${newStatus}`;
      const response: IApiRes<IRoamingService> = await axios.put(
        url,
        null,
        createCustomersCareHeader()
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  },

  async getServicesByCustomer(companyId: string): Promise<IApiRes<IPrimaryProduct[]>> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${B2C}/${CUSTOMERS_CARE}/${API}/${PRODUCT}/${COMPANIES}/${companyId}/${ME}/${SERVICES}`;
      const response: IApiRes<IPrimaryProduct[]> = await axios.get(
        url,
        createCustomersCareHeader()
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  },

  async getPrimaryProductsByContractProductId(
    companyId: string,
    contractProductId: string
  ): Promise<any> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${B2C}/${CUSTOMERS_CARE}/${API}/${PRODUCT}/${COMPANIES}/${companyId}/${ME}/${SERVICES}/${contractProductId}/${PRODUCTS}`;
      const response: IApiRes<IPrimaryProduct[]> = await axios.get(
        url,
        createCustomersCareHeader()
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  },

  async getRetrieveSubscriptionCDRConsumsByNumber(companyId: string, number: number): Promise<any> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${B2C}/${CUSTOMERS_CARE}/${API}/${PRODUCT}/${COMPANIES}/${companyId}/${ME}/${number}/${CDR}/${DETAILS}`;
      const response: IApiRes<any> = await axios.get(url, createCustomersCareHeader());
      return response;
    } catch (error: any) {
      return error.response;
    }
  },
  async sendMailToCustomer(documentNumber: string, company: string): Promise<any> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${CUSTOMERS_CARE}/${API}/${CUSTOMER}/${SEND_MAIL}/${documentNumber}`;
      const response: IApiRes<any> = await axios.post(url, { company: company });
      return response;
    } catch (error: any) {
      return error.response;
    }
  },

  async saveCustomerSocialMail(mail: string, token: string): Promise<any> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${CUSTOMERS_CARE}/${API}/${CUSTOMER}/${SAVE_SOCIAL_MAIL}/${mail}`;

      const formattedToken = `"${token}"`;

      const response: IApiRes<any> = await axios.post(url, formattedToken, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return response;
    } catch (error: any) {
      return error.response;
    }
  }
};

export default apiCustomersCareService;
