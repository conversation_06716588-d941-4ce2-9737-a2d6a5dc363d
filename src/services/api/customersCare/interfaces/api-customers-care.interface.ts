import type { ICustomerInfo } from '@/stores/customersCare/interfaces/customer-info.interface';
import type { IPicklist } from './picklist.interface';
import type { IApiRes } from '../../interfaces';
import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';

export interface IApiCustomersCare {
  getCustomerInfo(company: string): Promise<ICustomerInfo>;
  updateCustomerInfo<T extends keyof ICustomerInfo>(
    company: string,
    property: T,
    newCustomerInfo: ICustomerInfo[T] | ICustomerInfo[T][]
  ): Promise<
    IApiRes<{
      item: ICustomerInfo[T] | ICustomerInfo[T][];
    }>
  >;
  getRoamingService(company: string, subscriptionId: string): Promise<any>;
  activeRoamingService(company: string, subscriptionId: string, newStatus: boolean): Promise<any>;
  getPicklists(language: string): Promise<IPicklist[]>;
  getServicesByCustomer(companyId: string): Promise<IApiRes<IPrimaryProduct[]>>;
  getPrimaryProductsByContractProductId(companyId: string, contractProductId: string): Promise<any>;
  getRetrieveSubscriptionCDRConsumsByNumber(companyId: string, number: number): Promise<any>;
  sendMailToCustomer(documentNumber: string, company: string): Promise<any>;
  saveCustomerSocialMail(mail: string, token: string): Promise<any>;
}
