import axios from 'axios'
import {
  CONTENT_TYPE,
  APPLICATION_JSON_PATCH_JSON,
  API,
  X_PARLEM_API_KEY
} from '@/services/api/constants/apiConstants'
import { SHOPPING_CART, RATES, ADDONS } from '@/services/api/shopping/constants/shopping.constants'
import type { IApiRes, IHeaders } from '@/services/api/interfaces'
import type { IApiShopping } from './interfaces/api-shopping.interface'
import { PRODUCTS } from '../customersCare/constants/customersCare.constants'

const shoppingHeaders: IHeaders = {
  headers: {
    [X_PARLEM_API_KEY]: import.meta.env.VITE_API_KEY_SHOPPINGCART,
    [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON
  }
}

const apiShoppingService: IApiShopping = {
  async retrieveRateByCode(code: string): Promise<any> {
    try {
      const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPING_CART}/${API}/${RATES}/${code}`
      const response: IApiRes<any> = await axios.get(url, shoppingHeaders)
      return response && response.data
    } catch (error: any) {
      console.error(error)
      return error.response.status
    }
  },
  async retrieveAddonsRatesByRateCode(ratecode: string, productCode: string): Promise<any> {
    try {
      const url: string = `${import.meta.env.VITE_BASE_URL}/${SHOPPING_CART}/${API}/${RATES}/${'RP-1270' /* ratecode */}/${PRODUCTS}/${'IP-0527' /* productCode */}/${ADDONS}`
      const response: IApiRes<any> = await axios.post(url, [], shoppingHeaders)
      return response && response.data
    } catch (error: any) {
      console.error(error)
      return error.response.status
    }
  }
}

export default apiShoppingService
