import type { IConsumption } from '@/stores/customersCare/interfaces/consumption.interface';
import type { IDebtItem, IInvoicesItem } from '@/stores/customersCare/interfaces/invoice.interface';

export interface IApiOptimalPrice {
  getInvoiceByInvoiceNumber<T>(invoiceNum: string): Promise<T | void>;
  getInvoicesByNif(nif: string): Promise<IInvoicesItem>;
  getConsumptionByNumTelephone(consumptionPayload: {
    numTel: string;
    suscriptionId: string;
    company: string;
  }): Promise<IConsumption[]>;
  getDebtByNif(nif: string): Promise<IDebtItem>;
}
