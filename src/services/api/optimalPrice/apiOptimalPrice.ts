import axios from 'axios';
import {
  CONTENT_TYPE,
  APPLICATION_JSON_PATCH_JSON,
  API,
  BEARER,
  AUTHORIZATION,
  SERVICE,
  B2C,
  COMPANIES
} from '@/services/api/constants/apiConstants';
import { CUSTOMERS_CARE } from '@/services/api/customersCare/constants/customersCare.constants';
import {
  OPTIMAL_PRICE,
  CONSUMPTION,
  INVOICE,
  INVOICES,
  NUMTEL,
  CONTENT,
  DEBT,
  SUSCRIPTION
} from '@/services/api/optimalPrice/constants/optimal-price.constants';
import type { IApiRes, IHeaders } from '@/services/api/interfaces';
import { useAuthStore } from '@/stores/auth/index';
import type { IApiOptimalPrice } from './interfaces';
import type {
  IConsumption,
  IConsumptionItem
} from '@/stores/customersCare/interfaces/consumption.interface';
import type { IInvoicesItem, IDebtItem } from '@/stores/customersCare/interfaces/invoice.interface';

function createCustomersCareHeader() {
  const authStore = useAuthStore();
  const token = authStore.accessToken;

  const customersCareHeaders: IHeaders = {
    headers: {
      [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON,
      [AUTHORIZATION]: `${BEARER}${token}`,
      'Access-Control-Allow-Origin': '*'
    }
  };
  return customersCareHeaders;
}

const apiOptimalPrice: IApiOptimalPrice = {
  async getInvoiceByInvoiceNumber<T>(invoiceNum: string): Promise<T | void> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${B2C}/${CUSTOMERS_CARE}/${API}/${SERVICE}/${OPTIMAL_PRICE}/${INVOICE}/${CONTENT}`;
      const response: IApiRes<any> = await axios.post(
        url,
        { invoiceNum },
        createCustomersCareHeader()
      );
      return response && response.data;
    } catch (error: any) {
      return error.response;
    }
  },

  async getInvoicesByNif(nif: string): Promise<IInvoicesItem> {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${B2C}/${CUSTOMERS_CARE}/${API}/${SERVICE}/${OPTIMAL_PRICE}/${INVOICES}/${nif}`;
    const response: IApiRes<IInvoicesItem> = await axios.post(
      url,
      nif,
      createCustomersCareHeader()
    );
    return response && response.data;
  },

  async getConsumptionByNumTelephone(consumptionPayload: {
    numTel: string;
    suscriptionId: string;
    company: string;
  }): Promise<IConsumption[]> {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${B2C}/${CUSTOMERS_CARE}/${API}/${SERVICE}/${OPTIMAL_PRICE}/${CONSUMPTION}/${NUMTEL}/${consumptionPayload.numTel}/${COMPANIES}/${consumptionPayload.company}/${SUSCRIPTION}/${consumptionPayload.suscriptionId}`;
    const response: IApiRes<IConsumptionItem> = await axios.post(
      url,
      consumptionPayload.numTel,
      createCustomersCareHeader()
    );

    return response && response.data?.consumptions;
  },

  async getDebtByNif(nif: string): Promise<IDebtItem> {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${B2C}/${CUSTOMERS_CARE}/${API}/${SERVICE}/${OPTIMAL_PRICE}/${DEBT}/${nif}`;
    const response: IApiRes<IDebtItem> = await axios.post(url, nif, createCustomersCareHeader());
    return response && response.data;
  }
};

export default apiOptimalPrice;
