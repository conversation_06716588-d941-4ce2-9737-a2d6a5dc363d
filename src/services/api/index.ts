import axios from 'axios';
import apiCustomersCareService from '@/services/api/customersCare/apiCustomersCareService';
import apiOptimalPrice from '@/services/api/optimalPrice/apiOptimalPrice';
import apiShoppingService from '@/services/api/shopping/apiShopping';
import { logout } from '@/utils/msal-b2c/msalFunctions';
import { useAuthStore } from '@/stores/auth';

export { apiCustomersCareService, apiOptimalPrice, apiShoppingService };

axios.interceptors.response.use(
  (response) => response,
  (error) => {
    const authStore = useAuthStore();
    if (!error?.status && error.message === 'Network Error') {
      logout();
      authStore.setAuthenticationError(true);
      const url = new URL('/unauthorized', window.location.origin);
      window.location.href = url.toString();
    }

    return Promise.reject(error);
  }
);
