const savedLanguage = localStorage.getItem('selectedLanguage');

const getMonthYear = (lang: string) => {
  const date = new Date();
  return date.toLocaleDateString(lang, { year: 'numeric', month: 'long' });
};

const ListCallsFields = [
  {
    label: 'products.list-calls',
    labelKey: getMonthYear(savedLanguage || 'ca'),
    icon: 'fa-list',
    route: 'DetailCallsView',
    info: 'products.list-calls-info'
  }
];

export default ListCallsFields;
