<template>
  <div v-if="fields && fields.length">
    <p v-if="title" class="font-bold text-lg mb-1 mt-3">{{ title }}</p>
    <div class="flex flex-col gap-2">
      <WhiteCardComponent
        v-for="(field, index) in fields"
        :key="index"
        class="flex justify-between items-center p-2.5 cursor-pointer"
        @click="redirect(field.route)"
      >
        <div class="flex items-center">
          <div class="max-w-10 flex justify-center items-center p-1">
            <font-awesome-icon
              :icon="field.icon"
              class="text-gray dark:text-gray-dark size-[26px] mr-2"
            />
          </div>
          <div class="pl-2">
            <p class="text-lg font-medium">
              {{ $t(`${field.label}`, { key: `${field.labelKey}` }) }}
            </p>
            <p v-if="field.info" class="text-gray dark:text-gray-dark -mt-1 text-base/5">
              {{ $t(`${field.info}`) }}
            </p>
          </div>
        </div>
        <div class="w-10 flex justify-center items-center">
          <font-awesome-icon
            icon="fa-chevron-right"
            class="size-[24px] text-gray dark:text-gray-dark"
          />
        </div>
      </WhiteCardComponent>
    </div>
  </div>
</template>

<script lang="ts">
  import {
    computed,
    defineAsyncComponent,
    defineComponent,
    onMounted,
    ref,
    watch,
    type ComputedRef,
    type Ref
  } from 'vue';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  export default defineComponent({
    name: 'ManageProductsComponent'
  });
</script>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import productFields from './utils';
  import { useCustomersCare } from '@/stores/customersCare';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const props = defineProps({
    provisioningClass: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: ''
    }
  });
  const router = useRouter();
  const customersCareStore: any = useCustomersCare();
  const fields: Ref<any[]> = ref([]);
  const mainProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.getMainProduct
  );

  onMounted(() => {
    fields.value = productFields[props.provisioningClass];
  });

  const redirect = (destination: string) => {
    router.push({ name: destination, params: { id: mainProduct.value?.id } });
  };

  watch(
    () => props.provisioningClass,
    (newTypeOfProduct) => {
      fields.value = productFields[newTypeOfProduct] ? productFields[newTypeOfProduct] : [];
    }
  );
</script>
