<template>
  <WhiteCardComponent>
    <div v-for="(field, index) in formFields" :key="index" :class="`${index > 0 ? 'mt-2' : ''}`">
      <PwSelectAutocomplete
        v-if="field.type === 'selector'"
        v-model="field.value"
        :value="field.value?.label"
        item-title="label"
        item-value="value"
        :label="$t(field.label)"
        :placeholder="$t(field.placeholder)"
        :items="field.options"
        :customLabelClass="'!mb-0'"
        :customInputClass="'!p-2 !pl-3 !h-[42px]'"
        :clearDataIndex="clearDataIndex"
        :inputError="field.error"
      />
      <PwSelectBadges
        v-else-if="field.type === 'selectorBadges'"
        v-model="field.value"
        :value="field?.value?.value"
        item-title="label"
        item-value="value"
        :items="field.options"
        :filterValueOnFocus="''"
        :label="$t(field.label)"
        :placeholder="$t(field.placeholder)"
        :customLabelClass="'!mb-0'"
        :customInputClass="'!p-2 !pl-3'"
        :customBorderInputClass="'!h-[42px]'"
        :inputError="field.error"
        :clearDataIndex="clearDataIndex"
        :clearable="true"
      ></PwSelectBadges>
      <PwInputText
        v-else
        v-model="field.value"
        :type="field.type ? field.type : 'text'"
        :value="field.value"
        :label="$t(field.label)"
        :placeholder="$t(field.placeholder)"
        :clearable="field.type !== 'date'"
        :customLabelClass="'!mb-0'"
        :customInputClass="'!p-2 !pl-3 !h-[42px]'"
        :inputError="field.error"
        :validations="field.validations"
        :clearDataIndex="clearDataIndex"
        @errors="getErrors"
      />
    </div>
    <PwButton
      :text="buttonText"
      theme="primary-primary-light"
      class="px-2 min-w-20 mt-8 max-h-10"
      @click="submitForm"
      :disabled="hasErrors"
    >
    </PwButton>
  </WhiteCardComponent>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  export default defineComponent({
    name: 'FormComponent'
  });
</script>
<script setup lang="ts">
  import { onMounted, computed, defineAsyncComponent, ref } from 'vue';
  import type { ComputedRef, Ref } from 'vue';
  import {
    PwInputText,
    PwSelectAutocomplete,
    PwSelectBadges,
    PwButton
  } from 'parlem-webcomponents-common';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useRouter, useRoute } from 'vue-router';
  import formFieldOptions from '@/components/FormComponent/utils';
  import { useI18n } from 'vue-i18n';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const route = useRoute();
  const { t } = useI18n();
  const customersCareStore = useCustomersCare();
  const parentKey: Ref<any> = ref(route.params.key);
  const clearDataIndex: Ref<number> = ref(0);
  const formFields: Ref<any> = ref(null);
  const profileItemToEdit: ComputedRef<any> = computed(() => customersCareStore.profileItemToEdit);
  const emit = defineEmits(['submit']);
  const router = useRouter();

  const hasErrors = ref(false);

  function getErrors(errors: any[]) {
    hasErrors.value = errors.length > 0;
  }

  onMounted(() => {
    setFormFields();
  });

  const filterFormFields = (): void => {
    formFields.value = formFields.value.map((field: any) => {
      const fieldValue = profileItemToEdit.value[field.id];

      return { ...field, value: fieldValue };
    });
  };

  const setFormFields = (): void => {
    formFields.value = formFieldOptions[parentKey.value];
    if (route.params.id) {
      if (profileItemToEdit.value) {
        filterFormFields();
      } else {
        formFields.value = formFields.value.filter((field: any) => field.id === route.params.id);
      }
    } else if (route.path.includes('edit')) {
      if (!profileItemToEdit.value) {
        customersCareStore.setProfileItemToEdit(null);
        router.go(-1);
        return;
      }

      filterFormFields();
    }
    formFields.value.forEach((field: any) => {
      if (field.id === 'preferredLanguage') {
        const customerLanguage =
          customersCareStore.customerInfo?.preferredLanguage ||
          localStorage.getItem('selectedLanguage');

        field.value =
          field.options.find((option: any) => option.value === customerLanguage) ||
          field.options[0];
      } else if (['selector', 'selectorBadges'].includes(field.type)) {
        field.options.forEach((option: any) => {
          option.label = t(option.label);
        });
        field.value =
          field.options.find(
            (option: any) =>
              field.value && option.value?.toLowerCase() === field.value?.toLowerCase()
          ) || field.options[0];
      }
    });
  };

  const isSelectorField = (type: string): boolean => {
    return ['selector', 'selectorBadges'].includes(type);
  };
  const submitForm = () => {
    if (route.params.id === 'preferredLanguage') {
      emit('submit', formFields.value[0].value.value);
    } else if (profileItemToEdit.value) {
      formFields.value.forEach((field: any) => {
        profileItemToEdit.value[field.id] = isSelectorField(field.type)
          ? field.value.value
          : field.value;
      });
      emit('submit', profileItemToEdit.value);
    } else {
      const newObject: any = { isDefault: false };
      formFields.value.forEach((field: any) => {
        newObject[field.id] = isSelectorField(field.type) ? field.value.value : field.value;
      });
      emit('submit', newObject);
    }
    formFields.value = null;
    customersCareStore.setProfileItemToEdit(null);
  };

  const buttonText = computed(() => {
    if (route.params.id === 'preferredLanguage') {
      return t('profile.edit.settings.preferredLanguage');
    }
    return profileItemToEdit.value ? t('profile.edit.edit') : t('profile.create.create');
  });
</script>

<style></style>
