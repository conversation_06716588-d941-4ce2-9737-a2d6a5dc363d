import { i18n } from '@/i18n'
const t = i18n.t
const billingInfoFields: any[] = [
  {
    id: 'name',
    type: 'text',
    value: null,
    validations: ['required'],
    label: 'profile.billingInfos.name',
    placeholder: 'profile.billingInfos.name'
  },
  {
    id: 'cccOwner',
    type: 'text',
    value: null,
    validations: ['required'],
    label: 'profile.billingInfos.cccOwner',
    placeholder: 'profile.billingInfos.cccOwner'
  },
  {
    id: 'cccOwnerIdentification',
    type: 'text',
    value: null,
    validations: ['required', 'dni'],
    label: 'profile.billingInfos.cccOwnerIdentification',
    placeholder: 'profile.billingInfos.cccOwnerIdentification'
  },
  {
    id: 'iban',
    type: 'text',
    value: null,
    validations: ['required', 'iban'],
    label: 'profile.billingInfos.iban',
    placeholder: 'profile.billingInfos.iban'
  },
  {
    id: 'sendBill',
    selectorKey: 'sendBill',
    type: 'selector',
    value: null,
    validations: ['required'],
    label: 'profile.billingInfos.sendBill',
    placeholder: 'profile.billingInfos.sendBill',
    options: [
      { label: 'profile.billingInfos.sendBillFormat.email', value: 'Email' },
      { label: 'profile.billingInfos.sendBillFormat.paper', value: 'Paper' }
    ]
  }
]
export default billingInfoFields
