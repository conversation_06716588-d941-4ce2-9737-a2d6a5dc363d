import { i18n } from '@/i18n';
const t = i18n.t;

const settingFields: any[] = [
  {
    id: 'preferredLanguage',
    selectorKey: 'preferredLanguage',
    type: 'selector',
    value: null,
    validations: ['required'],
    label: 'profile.preferredLanguage',
    placeholder: 'profile.preferredLanguage',
    options: [
      { label: 'Català', value: 'ca' },
      { label: 'Valencià', value: 'va' },
      { label: 'Galego', value: 'gl' },
      { label: 'Español', value: 'es' },
      { label: 'English', value: 'en' },
      { label: 'Français', value: 'fr' },
      { label: 'Português', value: 'pt' },
      { label: 'Italiano', value: 'it' },
      { label: 'Русский (Russian)', value: 'ru' },
      { label: '中文 (Chinese)', value: 'zh' }
    ]
  }
];
export default settingFields;
