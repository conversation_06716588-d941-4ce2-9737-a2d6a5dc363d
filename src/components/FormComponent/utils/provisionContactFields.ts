const provisionContactFields: any[] = [
  {
    id: 'name',
    type: 'text',
    value: null,
    validations: ['required'],
    label: 'profile.provisionContacts.name',
    placeholder: 'profile.provisionContacts.name'
  },
  {
    id: 'email',
    type: 'email',
    value: null,
    validations: ['required', 'email'],
    label: 'profile.provisionContacts.email',
    placeholder: 'profile.provisionContacts.email'
  },
  {
    id: 'phone',
    type: 'phone',
    value: null,
    validations: ['required', 'phone'],
    label: 'profile.provisionContacts.phone',
    placeholder: 'profile.provisionContacts.phone'
  }
]
export default provisionContactFields
