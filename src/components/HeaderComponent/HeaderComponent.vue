<template>
  <div
    class="h-60 lg:h-[264px] 2xl:h-72 relative w-full max-w-[1040px] px-4 lg:px-6 flex flex-col lg:m-auto"
  >
    <!-- Logo -->
    <div class="flex justify-center">
      <img :src="logoImage" alt="Parlem Logo" loading="lazy" height="40" class="h-8 lg:h-10 mt-8" />
    </div>

    <!-- Icones -->
    <div class="flex justify-end mb-3 !pt-5 lg:!pt-9 2xl:!pt-12">
      <router-link :to="{ name: 'Help' }">
        <font-awesome-icon
          icon="fa-regular fa-circle-question"
          class="!size-[24px] text-white cursor-pointer"
          :title="$t('home.help')"
        ></font-awesome-icon>
      </router-link>
    </div>

    <!-- Nom client -->
    <div class="mt-4">
      <h1 class="!text-3xl lg:!text-4xl 2xl:!text-5xl font-bold text-white">
        {{ $t('home.title') }},
        {{
          customerInfo.personalData?.firstName?.charAt(0).toUpperCase() +
          customerInfo.personalData?.firstName?.slice(1).toLowerCase()
        }}!
      </h1>
    </div>

    <!-- Última Factura -->
    <div
      class="flex justify-center absolute w-full left-0 px-4 lg:px-6 -bottom-5 dark:text-white translate-y-1/3"
    >
      <WhiteCardComponent
        class="flex w-full items-center cursor-pointer max-h-40 hover:dark:bg-dark-hover dark:shadow-black hover:dark:shadow-dark-light hover:shadow-primary/50 hover:font-bold"
        @click="redirect('Billing')"
      >
        <div
          class="bg-background-color dark:bg-dark-gray-light size-20 rounded-full flex justify-center items-center"
        >
          <font-awesome-icon icon="fa-file-invoice" class="text-gray dark:text-white size-[48px]" />
        </div>
        <div class="pl-4">
          <p class="text-lg font-bold">
            {{ $t('home.last-invoice-available-title') }}
          </p>
          <p class="-my-0.5">{{ latestInvoiceMonth }}</p>
          <p class="text-2xl">{{ latestInvoicePrice }} €</p>
        </div>
      </WhiteCardComponent>
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'HeaderComponent'
  });
</script>

<script setup lang="ts">
  import { useCustomersCare } from '@/stores/customersCare';
  import { useI18n } from 'vue-i18n';
  import { useRouter } from 'vue-router';
  import { useAuthStore } from '@/stores/auth';
  import { type ComputedRef, computed, defineAsyncComponent } from 'vue';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const authStore: any = useAuthStore();
  const customersCareStore: any = useCustomersCare();
  const router = useRouter();

  const { t } = useI18n();
  const customerInfo = computed(() => customersCareStore?.customerInfo);
  const company: ComputedRef<string> = computed(() => authStore.company);

  const logoImage = `${import.meta.env.VITE_LOGOS_URL}/${company.value.toLowerCase()}-logo-white.webp`;

  const latestInvoice = computed(() => {
    const invoices = customersCareStore.invoicesByNif?.invoices || [];
    return (
      invoices.sort(
        (a: any, b: any) => new Date(b.fecEmision).getTime() - new Date(a.fecEmision).getTime()
      )[0] || {}
    );
  });

  const latestInvoicePrice = computed(() => latestInvoice.value?.totFactura || '0.00');

  const formatMonth = (fecEmision: string) => {
    const months: any = {
      '01': t('products.months.january'),
      '02': t('products.months.february'),
      '03': t('products.months.march'),
      '04': t('products.months.april'),
      '05': t('products.months.may'),
      '06': t('products.months.june'),
      '07': t('products.months.july'),
      '08': t('products.months.august'),
      '09': t('products.months.september'),
      '10': t('products.months.october'),
      '11': t('products.months.november'),
      '12': t('products.months.december')
    };

    if (!fecEmision) return '';
    const [year, month] = fecEmision.split('-');
    return `${months[month]} ${year}`;
  };

  const latestInvoiceMonth = computed(() => formatMonth(latestInvoice.value?.fecEmision));

  const redirect = (destination: string) => {
    router.push({ name: destination });
  };
</script>

<style scoped>
  header {
    width: 100vw;
  }
</style>
