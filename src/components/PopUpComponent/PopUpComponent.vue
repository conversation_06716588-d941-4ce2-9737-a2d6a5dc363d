<template>
  <PwPopup
    v-if="popUpItem"
    :close-button="true"
    :cancel-button="true"
    :cancel-button-text="$t(`popup.no`)"
    :accept-button="true"
    :accept-button-text="$t(`popup.yes`)"
    @close="closeModal"
    @accept="accept"
    @cancel="closeModal"
  >
    <div class="text-center p-10 flex flex-col items-center">
      <div class="mt-6">
        <div v-if="icon" class="mb-4">
          <font-awesome-icon :icon="icon" class="size-[80px] text-primary" />
        </div>
        <h3 class="text-2xl font-bold text-black dark:text-white">
          {{ title }}
        </h3>
        <p class="text-gray-500">
          {{ content }}
        </p>
      </div>
    </div>
  </PwPopup>
</template>

<script lang="ts">
export default {
  name: 'PwModal',
  props: {
    title: {
      type: String,
      required: false
    },
    content: {
      type: String,
      required: false
    },
    icon: {
      type: String,
      required: false
    }
  }
}
</script>

<script setup lang="ts">
import { PwPopup } from 'parlem-webcomponents-common'
import { computed } from 'vue'
import type { ComputedRef } from 'vue'
import { useCustomersCare } from '@/stores/customersCare'

const customersCareStore = useCustomersCare()
const emit = defineEmits(['close', 'accept'])

const popUpItem: ComputedRef<{
  type: string
  item: any
} | null> = computed(() => customersCareStore.popUpItem)

const closeModal = () => {
  customersCareStore.setPopUpItem(null)
  emit('close', false)
}

const accept = () => {
  emit('accept', popUpItem.value)
  customersCareStore.setPopUpItem(null)
}
</script>

<style></style>
