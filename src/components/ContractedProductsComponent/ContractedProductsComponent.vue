<template>
  <p class="font-bold text-xl mb-3 mt-5 dark:text-white">{{ $t('home.contracted-products') }}</p>
  <div v-if="isLoading" class="flex flex-col items-center">
    <PwLoading
      :company="company"
      loading-style="w-32 h-32"
      loading-image-style="w-[20px]"
      class="mt-8"
    ></PwLoading>
    <p class="font-bold mt-4 text-lg text-primary">{{ $t('general.loading') }}...</p>
  </div>
  <div
    v-else-if="services?.length > 0"
    class="grid !grid-cols-2 sm:!grid-cols-3 lg:!grid-cols-4 gap-3"
  >
    <WhiteCardComponent
      v-for="(service, index) in services"
      :key="index"
      class="flex flex-col items-center justify-center cursor-pointer shadow-sm hover:dark:bg-dark-hover dark:shadow-black hover:dark:shadow-dark-light hover:shadow-primary/50 hover:bg-white/60 hover:font-bold"
      @click="goToProducts(service)"
    >
      <div class="bg-primary-light rounded-full size-16 flex items-center mb-2 justify-center">
        <font-awesome-icon :icon="getIcon(service)" class="text-primary size-[36px]" />
      </div>
      <div>
        <p class="text-gray dark:text-gray-dark text-center font-medium">
          {{
            $t(
              `products.type.${service.provisioningClass ? service.provisioningClass.toLowerCase() : 'unknown'}`
            )
          }}
        </p>
        <p class="text-lg/5 dark:text-white text-center">
          {{ getServiceDisplayName(service) }}
        </p>
      </div>
    </WhiteCardComponent>
  </div>
  <WhiteCardComponent v-else class="flex flex-col items-center !py-10">
    <font-awesome-icon icon="fa-otter" class="size-[140px] text-gray mb-6" />
    <p class="font-bold text-center text-xl text-gray">{{ $t('products.no-available') }}</p>
  </WhiteCardComponent>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'ContractedProductsComponent'
  });
</script>

<script setup lang="ts">
  import { computed, defineAsyncComponent, type ComputedRef } from 'vue';
  import { useRouter } from 'vue-router';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useI18n } from 'vue-i18n';
  import { PwLoading } from 'parlem-webcomponents-common';
  import { useAuthStore } from '@/stores/auth/index';
  import type { IPrimaryProduct } from '../../stores/customersCare/interfaces/products.interface';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const { t } = useI18n();
  const customersCareStore: any = useCustomersCare();
  const router = useRouter();
  const authStore: any = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const isLoading: ComputedRef<boolean> = computed(() => authStore.isLoading);
  const services: ComputedRef<IPrimaryProduct[]> = computed(() => customersCareStore.services);

  const goToProducts = (service: IPrimaryProduct) => {
    customersCareStore.setSelectedService(service);
    localStorage.setItem('selectedService', JSON.stringify(service));
    router.push({ name: 'Products' });
  };

  const getIcon = (service: IPrimaryProduct) => {
    const provisioningClass = service.provisioningClass?.toLocaleLowerCase();
    switch (provisioningClass) {
      case 'fiber':
        return 'wifi';
      case 'tv':
        return 'tv';
      case 'mobile':
        return 'mobile-screen-button';
      case 'landline':
        return 'phone';
      case 'digitalservice':
        return 'hand-holding-hand';
      default:
        return 'minus';
    }
  };

  const getServiceDisplayName = (service: IPrimaryProduct) => {
    const provisioningClass: string = service.provisioningClass?.toLocaleLowerCase() || '';
    if (provisioningClass === 'fiber') {
      return service.fiber ? `${service.fiber.specifications.mbpsSpeed} Mb` : '-';
    } else if (['mobile', 'landline'].includes(provisioningClass)) {
      return service.number || '';
    } else {
      return service.productName || '';
    }
  };
</script>
