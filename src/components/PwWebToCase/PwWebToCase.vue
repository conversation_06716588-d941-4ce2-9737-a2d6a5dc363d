<template>
  <div id="pw-web-to-case" class="lg:m-4">
    <div v-if="isLoading" class="flex flex-col items-center">
      <PwLoading
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
        class="mt-8 mb-4"
      >
      </PwLoading>
      <p class="font-bold mt-4 text-lg text-primary">{{ $t('general.loading') }}...</p>
    </div>
    
  </div>
</template>

<script lang="ts">
  import { computed, defineComponent, onMounted, ref, watch, nextTick } from 'vue';
  import type { ComputedRef, Ref } from 'vue';
  export default defineComponent({ name: 'PwWebToCase' });
</script>

<script setup lang="ts">
  import { useCustomersCare } from '@/stores/customersCare';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';
  import { PwLoading } from 'parlem-webcomponents-common';
  import { useAuthStore } from '@/stores/auth/index';
  import createScript from '@/utils/createScript';

  const authStore: any = useAuthStore();
  const pwWebToCase: any = ref(null);
  const options: any = ref('app');
  const customersCareStore = useCustomersCare();
  const language: ComputedRef<string> = computed(() => customersCareStore.getLanguage) || 'ca';
  const customerInfo: ComputedRef<any> = computed(() => customersCareStore?.customerInfo);
  const route = useRoute();
  const isLoading: Ref<boolean> = ref(false);
  const url: Ref<string> = ref(window.location.href);
  const company: ComputedRef<string> = computed(() => authStore.company);

  const { t } = useI18n();

  onMounted(async () => {
    isLoading.value = true;
    if (customerInfo.value) {
      createScript('VITE_WEBTOCASE_WC', 'webtocase');
      await loadWebToCaseComponent();
    }
    isLoading.value = false;
  });

  const loadWebToCaseComponent = async (): Promise<void> => {
    const fields = [
      'name',
      'documentNumber',
      'email',
      'phone',
      'subject',
      'description',
      'category',
      'typology',
      'subtypology'
    ];
    const pathSegment = route.path.split('/')[1]?.toLowerCase() || 'default';
    const translatedSubject = t(`${pathSegment}.name`);
    const values = {
      url: url.value,
      name: customerInfo.value.personalData.completeName,
      documentNumber: customerInfo.value.personalData.documentNumber,
      email: customerInfo.value.provisionContacts[0].email,
      phone: customerInfo.value.provisionContacts[0].phone,
      subject: translatedSubject
    };

    setOptions();

    const webToCase = document.createElement('pw-web-to-case');
    webToCase.setAttribute('company', company.value);
    webToCase.setAttribute('lang', language.value);
    webToCase.setAttribute('fields', JSON.stringify(fields));
    webToCase.setAttribute('values', JSON.stringify(values));
    webToCase.setAttribute('options', options.value);
    await nextTick(); // Asegura que el DOM esté completamente renderizado antes de acceder al elemento

    pwWebToCase.value = document.getElementById('pw-web-to-case');
    if (pwWebToCase.value) {
      pwWebToCase.value.appendChild(webToCase);
    } else {
      console.error('El contenedor "pw-web-to-case" no está disponible en el DOM.');
    }
  };

  const setOptions = () => {
    const routeName = route.matched[0].name?.toString() || '';
    if (['Billing', 'Profile', 'Products'].includes(routeName)) {
      options.value = `app${routeName}`;
    } else {
      options.value = 'app';
    }
  };

  watch(customerInfo, async (newValue) => {
    if (newValue) {
      isLoading.value = true;
      createScript('VITE_WEBTOCASE_WC', 'webtocase');
      await loadWebToCaseComponent();
      isLoading.value = false;
    }
  });

  watch(route, async (newRoute) => {
    isLoading.value = true;
    createScript('VITE_WEBTOCASE_WC', 'webtocase');
    await loadWebToCaseComponent();
    isLoading.value = false;
  });
</script>
