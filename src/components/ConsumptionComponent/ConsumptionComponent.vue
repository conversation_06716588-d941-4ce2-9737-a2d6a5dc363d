<template>
  <WhiteCardComponent
    v-if="type === 'calls' || type === 'data'"
    class="flex flex-col justify-between items-start p-3"
  >
    <div class="flex justify-between items-start w-full">
      <div class="flex flex-col justify-between h-full">
        <!-- Titol i subtitol per tots -->
        <div>
          <p class="font-extrabold text-lg">{{ title }}</p>
          <p class="text-lg">{{ formatMonth(month) }}</p>
        </div>
        <p class="font-bold text-primary text-5xl mt-2">
          <!-- Trucades -->
          <span v-if="callsMobile && callsLandline">
            {{ getHoursPart(value) }}<span class="text-2xl">H </span>
            <span class="text-4xl">{{ getMinutesPart(value) }}</span>
            <span class="text-2xl">{{
              $t('products.consumption.diagram-chartBill-legend-calls-acronym')
            }}</span>

            <p
              v-if="
                mainProduct?.provisioningClass?.toLowerCase() === 'mobile' &&
                mainProduct?.mobile?.specifications?.minutesCallNational
              "
              class="mt-2 text-base/5 !text-gray dark:text-gray !font-semibold"
            >
              {{
                mainProduct?.mobile?.specifications?.minutesCallNational >= ilimitedCalls
                  ? $t('products.consumption.total-calls-minutes-unlimited')
                  : $t('products.consumption.total-calls-minutes', {
                      total: mainProduct?.mobile?.specifications?.minutesCallNational
                    })
              }}
            </p>
            <p
              v-if="
                mainProduct?.provisioningClass?.toLowerCase() === 'landline' &&
                (mainProduct?.landLine?.specifications?.minutesCallToFixNational ||
                  mainProduct?.landLine?.specifications?.minutesCallToMobileNational)
              "
              class="mt-2 text-base/5 !text-gray dark:text-gray !font-semibold"
            >
              {{
                mainProduct?.landLine?.specifications?.minutesCallToFixNational +
                  mainProduct?.landLine?.specifications?.minutesCallToMobileNational >=
                ilimitedCalls
                  ? $t('products.consumption.total-calls-minutes-unlimited')
                  : $t('products.consumption.total-calls-minutes', {
                      total:
                        mainProduct?.landLine?.specifications?.minutesCallToFixNational +
                        mainProduct?.landLine?.specifications?.minutesCallToMobileNational
                    })
              }}
            </p>
          </span>
          <!-- Dades -->
          <span v-else>
            <p v-if="parseFloat(value).toFixed(0) !== '0'">
              {{ parseFloat(value).toFixed(2) }} <span class="text-2xl">GB</span>
            </p>
            <p v-else>{{ (+value * 1024).toFixed(0) }} <span class="text-2xl">MB</span></p>
            <p
              v-if="type === 'data' && !callsMobile && !callsLandline"
              class="mt-2 text-base/5 !text-gray dark:text-gray !font-semibold"
            >
              {{
                fullUnit >= ilimitedData
                  ? $t('products.consumption.total-data-unlimited')
                  : $t('products.consumption.total-data', { total: fullUnit })
              }}
              <span
                v-if="
                  mobileProducts.length > 1 &&
                  mainProduct &&
                  isSharedProduct(mainProduct) &&
                  sharedMobiles.length > 1
                "
                >{{ $t('products.mobile.shared-data').toLowerCase() }}</span
              >
            </p>
          </span>
        </p>
      </div>
      <div class="size-36 md:size-40 flex items-center justify-center">
        <Doughnut class="absolute" :data="doughnutData" :options="chartOptions" />

        <!-- llegenda dades -->
        <div v-if="!callsLandline && !callsMobile" class="flex items-center justify-center">
          <div class="text-center text-lg">
            <p class="font-bold text-center" v-if="fullDataConsumed.toFixed(0) !== '0'">
              {{ fullDataConsumed.toFixed(2) }} GB
            </p>
            <p class="font-bold text-center" v-else>
              {{ (fullDataConsumed * 1024).toFixed(0) }} MB
            </p>

            <p class="text-xs md:text-sm">{{ $t('products.consumption.total-used') }}</p>
            <p
              class="text-xs md:text-sm"
              v-if="mobileProducts.length > 1 && mainProduct && isSharedProduct(mainProduct)"
            >
              {{ $t('products.mobile.shared-data').toLowerCase() }}
            </p>
          </div>
        </div>

        <!-- llegenda trucades -->
        <!--   <div
          v-else-if="callsLandline && callsMobile && month !== currentMonth"
          class="flex items-center justify-center"
        >
          <div class="text-md flex flex-col items-start">
            <div v-if="callsMobile">
              <font-awesome-icon
                icon="circle"
                :class="{
                  'text-primary-medium mr-1': !(callsLandline === '0.00' && callsMobile === '0.00'),
                  'text-gray dark:text-dark-background-hover mr-1':
                    callsLandline === '0.00' && callsMobile === '0.00'
                }"
              />
              <span> {{ $t('products.type.mobile') }}</span>
            </div>
            <div v-if="callsLandline">
              <font-awesome-icon
                icon="circle"
                :class="{
                  'text-primary mr-1': !(callsLandline === '0.00' && callsMobile === '0.00'),
                  'text-gray dark:text-dark-background-hover mr-1':
                    callsLandline === '0.00' && callsMobile === '0.00'
                }"
              />
              <span> {{ $t('products.type.landline') }} </span>
            </div>
          </div>
        </div> -->
        <div v-else-if="callsMobile" class="flex items-center justify-center">
          <div class="text-md flex flex-col items-center">
            <div v-if="callsMobile" class="flex">
              <font-awesome-icon
                icon="circle"
                class="mt-1"
                :class="{
                  'text-primary-medium mr-1': !(callsLandline === '0.00' && callsMobile === '0.00'),
                  'text-gray dark:text-dark-background-hover mr-1':
                    callsLandline === '0.00' && callsMobile === '0.00'
                }"
              />
              <p class="max-w-[60px] leading-5">
                <span> {{ $t('products.consumption.total-calls') }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </WhiteCardComponent>
</template>

<script lang="ts">
  export default {
    name: 'ConsumptionComponent'
  };
</script>

<script setup lang="ts">
  import { computed, ref, defineAsyncComponent, onMounted, watch } from 'vue';
  import type { Ref, ComputedRef } from 'vue';
  import { Doughnut } from 'vue-chartjs';
  import { Chart as ChartJS, ArcElement, Tooltip, Legend, Title } from 'chart.js';
  import { useI18n } from 'vue-i18n';
  import { getCssVaribleValue } from '@/utils/colors/getCssVaribleValue';
  import { useRouter } from 'vue-router';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  import { useCustomersCare } from '@/stores/customersCare';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  import type {
    IConsumption,
    IConsumptions
  } from '@/stores/customersCare/interfaces/consumption.interface';
  import { useAuthStore } from '@/stores/auth';
  import type { IAuthStore } from '@/stores/auth/interfaces/store.interface';

  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  ChartJS.register(ArcElement, Tooltip, Legend, Title);

  const props = defineProps({
    title: { type: String, required: true },
    month: { type: Number, required: true },
    year: { type: Number, required: true },
    value: { type: String, required: true },
    callsLandline: { type: String, required: false },
    callsMobile: { type: String, required: false },
    type: { type: String, required: false }
  });

  const { t } = useI18n();
  const router = useRouter();
  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const authStore: IAuthStore = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const themeMode = localStorage.getItem('theme');
  const isDarkMode: Ref<boolean> = ref(themeMode === 'dark');
  const currentMonth = ref(new Date().getMonth() + 1);
  const ilimitedCalls: number = 44640;
  const ilimitedData: number = 500;
  const chartOptions: any = ref({
    responsive: true,
    maintainAspectRatio: false,
    cutout: '75%',
    spacing: 2,
    plugins: {
      legend: { display: false },
      tooltip: {
        enabled: true,
        callbacks: {
          label: function (tooltipItem: any) {
            return props.type === 'data'
              ? `${tooltipItem.raw.toFixed(2)} GB`
              : `${getHoursPart(tooltipItem.raw.toFixed(2))}h ${getMinutesPart(tooltipItem.raw.toFixed(2))}${t(
                  'products.consumption.diagram-chartBill-legend-calls-acronym'
                ).toLowerCase()} ${getSecondsPart(tooltipItem.raw.toFixed(2))} ${t(
                  'products.consumption.diagram-chartBill-legend-calls-seconds-acronym'
                ).toLowerCase()}`;
          },
          title: function (tooltipItems: any) {
            return tooltipItems[0].label;
          }
        },
        bodyFont: {
          size: 12
        },
        titleFont: {
          size: 12
        }
      }
    }
  });
  const isLoadingConsumptions: Ref<boolean> = ref(false);
  const mainProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.getMainProduct
  );
  const mobileProducts: ComputedRef<IPrimaryProduct[]> = computed(() =>
    customersCareStore.getSelectedServiceProducts.filter(
      (product) =>
        product.provisioningClass?.toLowerCase() === 'mobile' ||
        product.provisioningClass?.toLowerCase() === 'refund'
    )
  );
  const allConsumptions: ComputedRef<IConsumptions> = computed(
    () => customersCareStore.getAllConsumptions
  );
  const fullUnit: Ref<number> = ref(0);
  const fullDataConsumed: Ref<number> = ref(0);
  const gbDataConsumed: Ref<number[]> = ref([]);
  const gbDataLabels: Ref<string[]> = ref([]);

  onMounted(async () => {
    initMobileData();
  });

  const initMobileData = async () => {
    if (props.type === 'calls') return;
    if (mobileProducts.value.length === 0) return;
    isLoadingConsumptions.value = true;
    getFullGBData();
    await getAllMobileConsumptions();
    getGbDataConsumed();
    isLoadingConsumptions.value = false;
  };

  const isSharedProduct = (product: IPrimaryProduct): boolean => {
    return (
      product?.provisioningSubClass.toLowerCase().includes('shared') ||
      product?.provisioningSubClass.includes('MobileLineExtra')
    );
  };

  const sharedMobiles = computed(() => {
    return mobileProducts.value.filter((product: IPrimaryProduct) => {
      return isSharedProduct(product);
    });
  });

  const getFullGBData = (): void => {
    fullUnit.value = 0;

    mobileProducts.value.forEach((product: IPrimaryProduct) => {
      if (!isActiveProduct(product)) {
        fullUnit.value += 0;
      } else if (product.provisioningClass?.toLowerCase() === 'refund') {
        fullUnit.value += product.refund?.gbsData || 0;
      } else if (
        mainProduct.value &&
        isSharedProduct(mainProduct.value) &&
        isSharedProduct(product)
      ) {
        fullUnit.value +=
          product.mobile?.specifications?.gbsDataNational ||
          product.mobile?.specifications?.gbsDataInternational ||
          0;
      } else if (
        mainProduct.value &&
        !isSharedProduct(mainProduct.value) &&
        !isSharedProduct(product) &&
        product.id === mainProduct.value?.id
      ) {
        fullUnit.value +=
          product.mobile?.specifications?.gbsDataNational ||
          product.mobile?.specifications?.gbsDataInternational ||
          0;
      }
    });
  };

  const getAllMobileConsumptions = async (): Promise<void> => {
    const promises = mobileProducts.value.map(async (product) => {
      if (
        mainProduct.value &&
        ((isSharedProduct(product) && isSharedProduct(mainProduct.value)) ||
          (!isSharedProduct(mainProduct.value) && mainProduct.value.id === product.id)) &&
        product.number &&
        !allConsumptions.value[product.number]
      ) {
        const consumptionPayload = {
          numTel: product.number,
          suscriptionId: product.contractedSubscriptionId || '',
          company: company.value
        };
        return await customersCareStore.getConsumptionByNumTelephone(consumptionPayload);
      }
    });

    await Promise.all(promises);
  };

  const getGbDataConsumed = async (): Promise<void> => {
    gbDataConsumed.value = [];
    gbDataLabels.value = [];
    fullDataConsumed.value = 0;
    //Creem l'objecte per passar al donut.
    mobileProducts.value.forEach(async (product: IPrimaryProduct) => {
      if (
        mainProduct.value &&
        ((isSharedProduct(product) && isSharedProduct(mainProduct.value)) ||
          (!isSharedProduct(mainProduct.value) && mainProduct.value.id === product.id)) &&
        product.number &&
        isActiveProduct(product)
      ) {
        const mobileConsumptionMonth: IConsumption | undefined = allConsumptions.value[
          product.number
        ]?.find(
          (consumption: IConsumption) =>
            consumption.mesConsumo === props.month && consumption.anoConsumo === props.year
        );
        const mobileProductConsumption: number = mobileConsumptionMonth?.datosKB
          ? mobileConsumptionMonth.datosKB / 1024 / 1024
          : 0;
        gbDataConsumed.value.push(mobileProductConsumption);
        fullDataConsumed.value += mobileProductConsumption;
        gbDataLabels.value.push(product.number);
      }
    });

    //Afegim el que falta per arribar a la quantitat total de dades.
    if (fullUnit.value < ilimitedData) {
      gbDataConsumed.value.push(
        fullUnit.value - fullDataConsumed.value > 0 ? fullUnit.value - fullDataConsumed.value : 0
      );
    }
    gbDataLabels.value.push(t('products.consumption.available'));
  };

  const isActiveProduct = (product: any): boolean => {
    if (product.endDate === null && product.activationDate === null) return true;
    if (product.endDate) {
      const endDate = new Date(product.endDate);
      const endDateTimestamp = endDate.getTime();
      const month = endDate.getMonth() + 1;
      const year = endDate.getFullYear();
      if (endDateTimestamp < 0) return false;
      if (year < props.year) return false;
      if (year === props.year && month < props.month) return false;
    }
    if (product.activationDate) {
      const activationDate = new Date(product.activationDate);
      const month = activationDate.getMonth() + 1;
      const year = activationDate.getFullYear();
      if (year > props.year) return false;
      if (year === props.year && month > props.month) return false;
    }
    return true;
  };

  const getHoursPart = (totalSeconds: string) => {
    const seconds = parseFloat(totalSeconds);
    return Math.floor(seconds / 3600);
  };

  const getMinutesPart = (totalSeconds: string) => {
    const seconds = parseFloat(totalSeconds);
    return Math.floor((seconds % 3600) / 60);
  };

  const getSecondsPart = (totalSeconds: string) => {
    const seconds = parseFloat(totalSeconds);
    return seconds % 60;
  };

  const getDounghnutDatasetsBackgroundColorForGigabites = (): string[] => {
    const colors: string[] = [];
    const printedMobileProducts: IPrimaryProduct[] = mobileProducts.value.filter(
      (product: IPrimaryProduct) =>
        mainProduct.value &&
        ((isSharedProduct(product) && isSharedProduct(mainProduct.value)) ||
          (!isSharedProduct(mainProduct.value) && mainProduct.value.id === product.id)) &&
        product.number &&
        isActiveProduct(product)
    );
    printedMobileProducts.forEach((product: IPrimaryProduct) => {
      if (product.id === mainProduct.value?.id) {
        colors.push(`rgb(${getCssVaribleValue('--color-primary')})`);
      } else {
        colors.push(`rgb(${getCssVaribleValue('--color-primary-medium')})`);
      }
    });
    colors.push(isDarkMode.value ? '#50575c' : '#e0e0e0');
    return colors;
  };

  const doughnutData = computed(() => {
    if (props.type === 'calls' && props.callsLandline === '0.00' && props.callsMobile === '0.00') {
      return {
        labels: [t('products.consumption.diagram-chartBill-legend-callsMobile')],
        datasets: [
          {
            data: [1],
            backgroundColor: isDarkMode.value ? ['#50575c'] : ['#e0e0e0'],
            borderWidth: isDarkMode.value ? 0 : 1
          }
        ]
      };
    } else if (props.callsLandline && props.callsMobile && props.month === currentMonth.value) {
      const mobile = parseFloat(props.callsMobile);
      return {
        labels: [t('products.consumption.total-calls')],
        datasets: [
          {
            data: [mobile],
            backgroundColor: isDarkMode.value
              ? [`rgb(${getCssVaribleValue('--color-primary-medium')})`, '#50575c']
              : [`rgb(${getCssVaribleValue('--color-primary-medium')})`, '#e0e0e0'],
            borderWidth: isDarkMode.value ? 0 : 1
          }
        ]
      };
    } else if (props.callsLandline && props.callsMobile && props.month !== currentMonth.value) {
      const landline = parseFloat(props.callsLandline);
      const mobile = parseFloat(props.callsMobile);
      return {
        labels: [
          /*           t('products.consumption.diagram-chartBill-legend-callsLandline'),
           */ t('products.consumption.diagram-chartBill-legend-callsMobile')
        ],
        datasets: [
          {
            data: [landline, mobile],
            backgroundColor: isDarkMode.value
              ? [
                  `rgb(${getCssVaribleValue('--color-primary')})`,
                  `rgb(${getCssVaribleValue('--color-primary-medium')})`,
                  '#50575c'
                ]
              : [
                  `rgb(${getCssVaribleValue('--color-primary')})`,
                  `rgb(${getCssVaribleValue('--color-primary-medium')})`,
                  '#e0e0e0'
                ],
            borderWidth: isDarkMode.value ? 0 : 1
          }
        ]
      };
    } else {
      return {
        labels: gbDataLabels.value,
        datasets: [
          {
            data: gbDataConsumed.value,
            backgroundColor: getDounghnutDatasetsBackgroundColorForGigabites(),
            borderWidth: isDarkMode.value ? 0 : 1,
            hoverOffset: 1
          }
        ]
      };
    }
  });

  const formatMonth = (month: number) => {
    const monthNames: any = {
      1: t('products.months.january'),
      2: t('products.months.february'),
      3: t('products.months.march'),
      4: t('products.months.april'),
      5: t('products.months.may'),
      6: t('products.months.june'),
      7: t('products.months.july'),
      8: t('products.months.august'),
      9: t('products.months.september'),
      10: t('products.months.october'),
      11: t('products.months.november'),
      12: t('products.months.december')
    };

    const fullMonth = monthNames[month];
    return `${fullMonth} ${props.year}`;
  };

  const goToDetailCalls = () => {
    router.push({ name: 'DetailCallsView', params: { id: mainProduct.value?.id } });
  };

  watch(mainProduct, async () => {
    initMobileData();
  });

  watch(
    () => props.month,
    async () => {
      initMobileData();
    }
  );
</script>
