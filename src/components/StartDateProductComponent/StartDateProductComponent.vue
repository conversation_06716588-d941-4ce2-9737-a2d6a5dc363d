<template>
  <WhiteCardComponent v-if="mainProduct" class="flex justify-between items-center p-2.5">
    <div class="flex items-center">
      <div class="max-w-10 flex justify-center items-center p-1">
        <font-awesome-icon
          icon="fa-calendar-days"
          class="text-gray dark:text-gray-dark size-[26px] mr-2"
        />
      </div>
      <div class="pl-2">
        <p class="text-lg font-medium">
          {{ $t('products.start-date') }}
        </p>
        <p class="text-gray dark:text-gray-dark -mt-1 text-base/5">
          {{ new Date(mainProduct?.activationDate).toLocaleDateString(language, dateOptions) }}
        </p>
      </div>
    </div>
  </WhiteCardComponent>
</template>

<script setup lang="ts">
  import { useCustomersCare } from '@/stores/customersCare';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  import { computed, defineAsyncComponent, type ComputedRef } from 'vue';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';

  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const mainProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.getMainProduct
  );
  const language: ComputedRef<string | undefined> = computed(() => customersCareStore.getLanguage);
  const dateOptions: any = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };
</script>
