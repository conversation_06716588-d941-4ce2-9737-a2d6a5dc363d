<template>
  <div class="flex items-center mt-4 mb-3">
    <p class="font-bold text-xl">{{ mainProduct?.productName }}</p>
    <font-awesome-icon
      v-if="mainProduct?.rateCode"
      icon="fa-circle-info"
      @click="goToRateDetails"
      class="ml-4 size-[22px] text-gray dark:text-gray-dark cursor-pointer"
    />
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { useCustomersCare } from '@/stores/customersCare';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  import { computed, type ComputedRef } from 'vue';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';

  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const router = useRouter();
  const mainProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.getMainProduct
  );
  const goToRateDetails = () => {
    if (mainProduct.value?.rateCode) {
      router.push({ name: 'RateDetailView', params: { rateCode: mainProduct.value.rateCode } });
    }
  };
</script>
