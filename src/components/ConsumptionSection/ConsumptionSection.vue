<template>
  <section v-if="mainProduct">
    <p class="font-bold text-lg mb-1 mt-3">
      {{ $t('products.consumption.name') }} {{ mainProduct.number }}
    </p>
    <div class="flex flex-col gap-2 mt-2">
      <WhiteCardComponent v-if="loadingConsumption" class="flex flex-col items-center py-6">
        <PwLoading
          :company="company"
          loading-style="w-32 h-32"
          loading-image-style="w-[20px]"
          class="mt-8"
        />
        <p class="font-bold mt-4 mb-8 text-lg text-primary">
          {{ $t('products.consum-loading') }} ...
        </p>
      </WhiteCardComponent>
      <WhiteCardComponent v-if="noDataMessage" class="flex flex-col items-center !py-10">
        <font-awesome-icon icon="fa-chart-column" class="size-[150px] text-gray mb-4" />
        <p class="font-bold text-center text-xl text-gray">{{ noDataMessage }}</p>
      </WhiteCardComponent>

      <div v-else>
        <ChartConsumptionComponent
          v-if="isDataLoaded"
          :calls="selectedCalls"
          :callsLandline="selectedCallsLandline"
          :callsMobile="selectedCallsMobile"
          :dataGb="selectedDataGb"
          :monthLabels="monthYearLabels"
          @updateSelectedMonth="updateSelectedMonth"
          :productType="mainProduct.provisioningClass"
        />

        <div class="consumption-container mt-3 gap-3" v-if="!loadingConsumption">
          <ConsumptionComponent
            v-if="mainProduct.provisioningClass === 'Mobile'"
            class="w-full"
            :title="$t('products.consumption.diagram-circle-consumption-data')"
            :month="selectedMonth"
            :year="selectedYear"
            :value="dataForSelectedMonth"
            type="data"
          />
          <ConsumptionComponent
            class="w-full"
            :title="$t('products.consumption.diagram-circle-consumption-calls')"
            :month="selectedMonth"
            :year="selectedYear"
            :value="callsForSelectedMonth"
            :callsLandline="callsLandlineForSelectedMonth"
            :callsMobile="callsMobileForSelectedMonth"
            type="calls"
          />
        </div>
      </div>
      <ManageProductsComponent provisioningClass="list-calls"></ManageProductsComponent>
      <StartDateProductComponent></StartDateProductComponent>

      <div>
        <p class="font-bold text-lg mb-1 mt-3">{{ $t('products.rate.characteristics') }}</p>
        <WhiteCardComponent>
          <div
            v-if="['mobile'].includes(mainProduct?.provisioningClass?.toLowerCase())"
            class="grid grid-cols-2 w-full"
          >
            <div class="flex flex-col items-center justify-between">
              <p class="font-bold">{{ $t('products.mobile.data') }}</p>
              <p
                v-if="
                  mainProduct?.mobile?.specifications.gbsDataNational ||
                  mainProduct?.mobile?.specifications.gbsDataNational === 0
                "
                class="text-lg lg:text-xl text-primary text-center font-bold"
              >
                <span class="text-2xl lg:text-4xl">{{
                  mainProduct?.mobile?.specifications.gbsDataNational >= ilimitedData
                    ? ''
                    : mainProduct?.mobile?.specifications.gbsDataNational
                }}</span>
                <span
                  :class="
                    mainProduct?.mobile?.specifications.gbsDataNational >= ilimitedData
                      ? 'text-xl sm:text-2xl lg:text-3xl'
                      : ''
                  "
                  >{{
                    mainProduct?.mobile?.specifications.gbsDataNational >= ilimitedData
                      ? `${$t('products.consumption.unlimited')}`
                      : ` GB `
                  }}</span
                >
                <span
                  v-if="
                    mobileProducts &&
                    mobileProducts.length > 1 &&
                    isSharedProduct(mainProduct) &&
                    (sharedMobiles?.length || 0) > 1
                  "
                  >{{
                    mainProduct?.mobile?.specifications.gbsDataNational > 1
                      ? `${$t('products.mobile.shared-data')}`
                      : `${$t('products.mobile.shared-data-single')}`
                  }}</span
                >
              </p>
            </div>
            <div class="flex flex-col text-center items-center justify-between">
              <p class="font-bold">{{ $t('products.mobile.calls-national') }}</p>
              <p
                v-if="mainProduct?.mobile?.specifications.minutesCallNational"
                class="text-lg text-center text-primary font-bold"
              >
                <span class="text-2xl lg:text-4xl">{{
                  mainProduct?.mobile?.specifications.minutesCallNational >= ilimitedCalls
                    ? ''
                    : mainProduct?.mobile?.specifications.minutesCallNational
                }}</span>
                <span
                  :class="
                    mainProduct?.mobile?.specifications.minutesCallNational >= ilimitedCalls
                      ? 'text-xl sm:text-2xl lg:text-3xl'
                      : ''
                  "
                  >{{
                    mainProduct?.mobile?.specifications.minutesCallNational >= ilimitedCalls
                      ? `${$t('products.consumption.unlimited')}`
                      : ` ${$t('products.minuts')}`
                  }}</span
                >
              </p>
              <p v-else class="text-lg text-center text-primary font-bold">
                <span class="text-2xl lg:text-4xl">0</span> {{ `${$t('products.minuts')}` }}
              </p>
            </div>
          </div>
          <div
            class="grid grid-cols-2 w-full"
            v-if="['landline'].includes(mainProduct?.provisioningClass?.toLowerCase())"
          >
            <div class="flex flex-col items-center justify-between">
              <p class="font-bold">{{ $t('products.mobile.calls-to-mobile') }}</p>
              <p
                v-if="mainProduct?.landLine?.specifications?.minutesCallToMobileNational"
                class="text-lg text-primary font-bold"
              >
                <span class="text-2xl lg:text-4xl">{{
                  mainProduct?.landLine?.specifications?.minutesCallToMobileNational >=
                  ilimitedCalls
                    ? ''
                    : mainProduct?.landLine?.specifications?.minutesCallToMobileNational + ''
                }}</span>
                <span
                  v-if="mainProduct?.landLine?.specifications?.minutesCallToMobileNational"
                  :class="
                    mainProduct?.landLine?.specifications?.minutesCallToMobileNational >=
                    ilimitedCalls
                      ? 'text-xl sm:text-2xl lg:text-3xl'
                      : ''
                  "
                  >{{
                    mainProduct?.landLine?.specifications?.minutesCallToMobileNational >=
                    ilimitedCalls
                      ? `${$t('products.consumption.unlimited')}`
                      : ` ${$t('products.minuts')}`
                  }}</span
                >
              </p>
              <p v-else class="text-lg text-center text-primary font-bold">
                <span class="text-2xl lg:text-4xl">0</span> {{ `${$t('products.minuts')}` }}
              </p>
            </div>
            <div class="flex flex-col items-center justify-between">
              <p class="font-bold">{{ $t('products.mobile.calls-to-landline') }}</p>
              <p
                v-if="mainProduct?.landLine?.specifications?.minutesCallToFixNational"
                class="text-lg text-primary font-bold"
              >
                <span
                  :class="
                    mainProduct?.landLine?.specifications?.minutesCallToMobileNational >=
                    ilimitedCalls
                      ? 'text-xl sm:text-2xl lg:text-3xl'
                      : 'text-2xl lg:text-4xl'
                  "
                  >{{
                    mainProduct?.landLine?.specifications?.minutesCallToFixNational >= ilimitedCalls
                      ? `${$t('products.consumption.unlimited')}`
                      : mainProduct?.landLine?.specifications?.minutesCallToFixNational +
                        ` ${$t('products.minuts')}`
                  }}</span
                >
              </p>
              <p v-else class="text-lg text-center text-primary font-bold">
                <span class="text-2xl lg:text-4xl">0</span> {{ `${$t('products.minuts')}` }}
              </p>
            </div>
          </div>
        </WhiteCardComponent>
        <WhiteCardComponent
          v-show="refundProducts && refundProducts.length > 0"
          v-for="(refundProduct, index) in refundProducts"
          :key="index"
          class="mt-3"
        >
          <div class="w-full" v-if="refundProduct?.refund?.gbsData">
            <div class="flex flex-col items-center justify-between">
              <p class="font-bold">{{ $t('products.refund.data') }}</p>
              <p class="text-lg lg:text-xl text-primary text-center font-bold">
                <span class="text-2xl lg:text-4xl">{{ refundProduct?.refund?.gbsData }}</span>
                GB
                <span v-if="mobileProducts && mobileProducts?.length > 1">{{
                  refundProduct?.refund?.gbsData > 1
                    ? `${$t('products.mobile.shared-data')}`
                    : `${$t('products.mobile.shared-data-single')}`
                }}</span>
              </p>
            </div>
          </div>
          <div
            v-if="
              refundProduct?.refund?.minutesCallToMobileNational ||
              refundProduct?.refund?.minutesCallToFixNational
            "
            class="w-full grid grid-cols-2"
          >
            <div class="flex flex-col text-center items-center justify-between">
              <p class="font-bold">{{ $t('products.mobile.calls-to-mobile') }}</p>
              <p
                v-if="refundProduct?.refund?.minutesCallToMobileNational"
                class="text-lg text-center text-primary font-bold"
              >
                <span class="text-2xl lg:text-4xl">{{
                  refundProduct?.refund?.minutesCallToMobileNational >= ilimitedCalls
                    ? ''
                    : refundProduct?.refund?.minutesCallToMobileNational
                }}</span>
                <span
                  :class="
                    refundProduct?.refund?.minutesCallToMobileNational >= ilimitedCalls
                      ? 'text-xl sm:text-2xl lg:text-3xl'
                      : ''
                  "
                  >{{
                    refundProduct?.refund?.minutesCallToMobileNational >= ilimitedCalls
                      ? `${$t('products.consumption.unlimited')}`
                      : ` ${$t('products.minuts')}`
                  }}</span
                >
              </p>
              <p v-else class="text-lg text-center text-primary font-bold">
                <span class="text-2xl lg:text-4xl">0</span> {{ `${$t('products.minuts')}` }}
              </p>
            </div>
            <div class="flex flex-col items-center text-center justify-between">
              <p class="font-bold">{{ $t('products.mobile.calls-to-landline') }}</p>
              <p
                v-if="refundProduct?.refund?.minutesCallToFixNational"
                class="text-lg text-primary font-bold"
              >
                <span class="text-2xl lg:text-4xl">{{
                  refundProduct?.refund?.minutesCallToFixNational >= ilimitedCalls
                    ? ''
                    : refundProduct?.refund?.minutesCallToFixNational
                }}</span>
                <span
                  :class="
                    refundProduct?.refund?.minutesCallToFixNational >= ilimitedCalls
                      ? 'text-xl sm:text-2xl lg:text-3xl'
                      : ''
                  "
                  >{{
                    refundProduct?.refund?.minutesCallToFixNational >= ilimitedCalls
                      ? `${$t('products.consumption.unlimited')}`
                      : ` ${$t('products.minuts')}`
                  }}</span
                >
              </p>
              <p v-else class="text-lg text-center text-primary font-bold">
                <span class="text-2xl lg:text-4xl">0</span> {{ `${$t('products.minuts')}` }}
              </p>
            </div>
          </div>
        </WhiteCardComponent>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
  export default {
    name: 'ConsumptionSection'
  };
</script>
<script setup lang="ts">
  import type {
    IPrimaryProduct,
    IProducts
  } from '@/stores/customersCare/interfaces/products.interface';
  import { computed, defineAsyncComponent, ref, watch, type ComputedRef, type Ref } from 'vue';
  import type { IChartDataValue } from '../ProductInfoComponent/utils/chart-data-value.interface';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  import { useCustomersCare } from '@/stores/customersCare';
  import { PwLoading } from 'parlem-webcomponents-common';
  import type { IConsumption } from '@/stores/customersCare/interfaces/consumption.interface';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';
  import type { IAuthStore } from '@/stores/auth/interfaces/store.interface';
  import { useAuthStore } from '@/stores/auth';

  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const ChartConsumptionComponent = defineAsyncComponent(
    () => import('@/components/ChartConsumptionComponent/ChartConsumptionComponent.vue')
  );
  const ConsumptionComponent = defineAsyncComponent(
    () => import('@/components/ConsumptionComponent/ConsumptionComponent.vue')
  );
  const ManageProductsComponent = defineAsyncComponent(
    () => import('@/components/ManageProductsComponent/ManageProductsComponent.vue')
  );
  const StartDateProductComponent = defineAsyncComponent(
    () => import('@/components/StartDateProductComponent/StartDateProductComponent.vue')
  );
  const { t } = useI18n();
  const route = useRoute();
  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const authStore: IAuthStore = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const loadingConsumption: Ref<boolean> = ref(false);
  const selectedCalls: Ref<IChartDataValue[]> = ref([]);
  const selectedCallsLandline: Ref<IChartDataValue[]> = ref([]);
  const selectedCallsMobile: Ref<IChartDataValue[]> = ref([]);
  const selectedDataGb: Ref<IChartDataValue[]> = ref([]);
  const monthYearLabels: Ref<string[]> = ref([]);
  const dataForSelectedMonth: Ref<string> = ref('');
  const selectedMonth: Ref<number> = ref(0);
  const selectedYear: Ref<number> = ref(0);
  const callsForSelectedMonth: Ref<string> = ref('');
  const callsMobileForSelectedMonth: Ref<string> = ref('');
  const callsLandlineForSelectedMonth: Ref<string> = ref('');
  const noDataMessage: Ref<string> = ref('');
  const isDataLoaded: Ref<boolean> = ref(false);
  const ilimitedCalls: number = 44640;
  const ilimitedData: number = 500;
  const language: ComputedRef<string | undefined> = computed(() => customersCareStore.getLanguage);
  const products: ComputedRef<IProducts | null> = computed(() => customersCareStore.products);
  const mainProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.getMainProduct
  );
  const isSharedProduct = (product: IPrimaryProduct): boolean => {
    return (
      product?.provisioningSubClass.toLowerCase().includes('shared') ||
      product?.provisioningSubClass.includes('MobileLineExtra')
    );
  };

  const refundProducts: ComputedRef<IPrimaryProduct[] | null> = computed(() => {
    if (products.value && mainProduct.value) {
      return products.value?.[mainProduct.value?.id]?.filter(
        (product: IPrimaryProduct) => product.provisioningClass?.toLowerCase() === 'refund'
      );
    }
    return null;
  });
  const mobileProducts: ComputedRef<IPrimaryProduct[] | null> = computed(() => {
    if (products.value && mainProduct.value) {
      return products.value?.[mainProduct.value?.id]?.filter(
        (product: IPrimaryProduct) => product.provisioningClass?.toLowerCase() === 'mobile'
      );
    }
    return null;
  });

  const sharedMobiles = computed(() => {
    return mobileProducts.value?.filter((product: IPrimaryProduct) => {
      return isSharedProduct(product);
    });
  });

  const phoneConsumptions: ComputedRef<IConsumption[]> = computed(
    () => customersCareStore.getPhoneConsumptions
  );

  const selectProductInfo = async (product: any) => {
    const provisioningClass = product?.provisioningClass?.toLowerCase();

    if (['mobile', 'landline'].includes(provisioningClass)) {
      loadingConsumption.value = true;
      noDataMessage.value = '';
      isDataLoaded.value = false;

      try {
        if (!phoneConsumptions.value || phoneConsumptions.value.length === 0) {
          const consumptionPayload = {
            numTel: product.number,
            suscriptionId: product.contractedSubscriptionId || '',
            company: company.value
          };
          await customersCareStore.getConsumptionByNumTelephone(consumptionPayload);
        }
        if (!phoneConsumptions.value || phoneConsumptions.value.length === 0) {
          noDataMessage.value = t('products.consumption.no-data');
          selectedCalls.value = [];
          selectedCallsLandline.value = [];
          selectedCallsMobile.value = [];
          selectedDataGb.value = [];
          monthYearLabels.value = [];
        } else {
          selectedCalls.value = phoneConsumptions.value.map(
            (consumption: IConsumption): IChartDataValue => ({
              x: consumption.mesConsumo,
              y: (consumption.segMovil + consumption.segFijo).toFixed(2)
            })
          );
          selectedCallsLandline.value = phoneConsumptions.value.map(
            (consumption: IConsumption): IChartDataValue => ({
              x: consumption.mesConsumo,
              y: consumption.segFijo.toFixed(2)
            })
          );
          selectedCallsMobile.value = phoneConsumptions.value.map(
            (consumption: IConsumption): IChartDataValue => ({
              x: consumption.mesConsumo,
              y: consumption.segMovil.toFixed(2)
            })
          );
          selectedDataGb.value = phoneConsumptions.value.map(
            (consumption: IConsumption): IChartDataValue => ({
              x: consumption.mesConsumo,
              y: (consumption.datosKB / 1024 / 1024).toString()
            })
          );
          monthYearLabels.value = phoneConsumptions.value.map((consumption: IConsumption): string =>
            formatMonthAcronym(consumption.mesConsumo, consumption.anoConsumo)
          );
          updateSelectedMonth(monthYearLabels.value.length - 1);
          isDataLoaded.value = true;
        }
      } catch (error) {
        console.error('Error loading consumption data:', error);
        noDataMessage.value = t('products.consumption.error');
      } finally {
        loadingConsumption.value = false;
      }
    }
  };

  const formatMonthAcronym = (month: number, year: number) => {
    const shortMonth = new Date(+`${year}`, month - 1).toLocaleDateString(language.value, {
      month: 'short'
    });
    return `${shortMonth.charAt(0).toUpperCase() + shortMonth.slice(1)} ${year.toString().slice(-2)}`;
  };

  const updateSelectedMonth = (index: any) => {
    const roundIndex = Math.floor(index);
    selectedMonth.value = phoneConsumptions.value[roundIndex].mesConsumo;
    selectedYear.value = phoneConsumptions.value[roundIndex].anoConsumo;
    dataForSelectedMonth.value = selectedDataGb.value[roundIndex].y;
    callsForSelectedMonth.value = selectedCalls.value[roundIndex].y;
    callsMobileForSelectedMonth.value = selectedCallsMobile.value[roundIndex].y;
    callsLandlineForSelectedMonth.value = selectedCallsLandline.value[roundIndex].y;
  };

  watch(mainProduct, async (newProduct: IPrimaryProduct | null) => {
    if (!newProduct) return;
    await selectProductInfo(newProduct);
  });

  watch(
    () => route,
    async (newRoute, oldRoute) => {
      if (newRoute.path !== oldRoute?.path && newRoute.path.includes('products')) {
        selectProductInfo(mainProduct.value);
      }
    },
    { immediate: true, deep: true }
  );
</script>

<style scoped>
  .consumption-container {
    display: flex;
    flex-direction: column;
  }

  @media (min-width: 768px) {
    .consumption-container {
      flex-direction: row;
    }
  }
</style>
