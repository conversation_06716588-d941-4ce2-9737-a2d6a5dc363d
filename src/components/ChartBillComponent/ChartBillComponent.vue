<template>
  <WhiteCardComponent class="flex flex-col gap-2 p-4">
    <div v-if="visibleInvoices && visibleInvoices.length">
      <div class="flex flex-col">
        <div class="flex justify-between">
          <p class="text-xl mb-2" v-if="selectedInvoice">
            {{ $t(`billing.invoice.total-month`) }} {{ getMonth(selectedInvoice.fecEmision) }}
            {{ getYear(selectedInvoice.fecEmision) }}
          </p>

          <div
            v-if="
              currentInvoice && currentInvoice.debt?.impDebe && currentInvoice.debt?.impDebe > 0
            "
          >
            <PwStatus
              v-if="getMonthNumber(currentInvoice.fecEmision) < getMonthNumber(new Date())"
              :text-status="$t('billing.invoice.status-invoice-pending')"
              :style-status="'bg-error-light text-error text-center'"
              class="!text-lg !w-24"
            />
            <PwStatus
              v-if="getMonthNumber(currentInvoice.fecEmision) === getMonthNumber(new Date())"
              :text-status="$t('billing.invoice.status-invoice-current-month')"
              :style-status="'bg-blue-light text-blue text-center'"
              class="!text-lg !w-24"
            />
          </div>
        </div>

        <div class="flex items-end gap-1 mb-2">
          <p v-if="isDetailInvoice" class="font-bold text-5xl leading-12">
            {{ currentInvoicePrice }}€
          </p>
          <p v-if="isDetailInvoice">
            {{ $t(`billing.invoice.total-month-taxes-included`) }}
          </p>
        </div>
      </div>

      <div class="text-base mb-3" v-if="isDetailInvoice">
        <p class="my-1">{{ $t(`billing.invoice.date-of-issue`) }}: {{ currentInvoiceDate }}</p>
        <p class="my-1">
          {{ $t(`billing.invoice.date-of-payment`) }}: {{ currentInvoicePaymentDate }}
        </p>
        <p class="my-1">{{ $t(`billing.invoice.invoice-number`) }}: {{ currentInvoiceNumber }}</p>
      </div>

      <div
        ref="scrollContainer"
        class="flex overflow-y-hidden overflow-x-auto h-[200px] w-full md:h-[350px] mt-4 pb-2"
        @scroll="handleScroll"
      >
        <Bar
          v-for="(visibleInvoice, index) in visibleInvoices"
          :key="index"
          :data="chartData(visibleInvoice)"
          :options="chartOptions(visibleInvoice, index)"
        />
      </div>

      <div class="flex justify-center gap-3 my-1 mt-4" v-if="totalPages > 1">
        <div
          v-for="(dot, index) in totalPages"
          :key="index"
          :class="['w-3 h-3 rounded-full', currentPage === index + 1 ? 'bg-primary' : 'bg-gray']"
          @click="goToPage(index + 1)"
          class="cursor-pointer"
        ></div>
      </div>
      <PwButton
        class="mt-3 mb-0"
        :text="$t(`billing.invoice.button-view-invoice`)"
        :theme="'primary-light'"
        @click="goInvoiceView(currentInvoiceNumber)"
      ></PwButton>
    </div>
    <div v-else class="flex flex-col items-center justify-center py-20 px-4">
      <div class="p-4 rounded-full">
        <font-awesome-icon
          icon="fa-regular fa-circle-xmark"
          class="size-[70px] font-bold text-error"
        ></font-awesome-icon>
      </div>
      <p class="font-bold text-lg text-error">Hi ha hagut un error</p>
      <p v-if="errorMessage" class="font-bold text-lg text-error">{{ errorMessage }}</p>
    </div>
  </WhiteCardComponent>
</template>

<script setup lang="ts">
  import { Bar } from 'vue-chartjs';
  import {
    Chart as ChartJS,
    Title,
    Tooltip,
    Legend,
    BarElement,
    CategoryScale,
    LinearScale
  } from 'chart.js';
  import { PwButton, PwStatus } from 'parlem-webcomponents-common';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useI18n } from 'vue-i18n';
  import { useRouter } from 'vue-router';
  import { getCssVaribleValue } from '@/utils/colors/getCssVaribleValue';
  import {
    ref,
    watch,
    watchEffect,
    computed,
    onMounted,
    nextTick,
    defineAsyncComponent
  } from 'vue';
  import type { Ref, ComputedRef } from 'vue';
  import type {
    IInvoice,
    IInvoicesItem
  } from '@/stores/customersCare/interfaces/invoice.interface';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const router = useRouter();
  ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale);

  const { t } = useI18n();
  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const isDetailInvoice: Ref<boolean> = ref(false);
  const selectedInvoice: Ref<IInvoice | null> = ref(null);
  const invoices: ComputedRef<IInvoice[]> = computed(() => {
    let invoices: IInvoice[] = customersCareStore.invoicesByNif?.invoices || [];
    if (invoices.length > 0) {
      invoices = invoices.sort(
        (invoiceA: IInvoice, invoiceB: IInvoice) =>
          new Date(invoiceB.fecEmision).getTime() - new Date(invoiceA.fecEmision).getTime()
      );
    }
    return invoices;
  });
  const columnsPerPage: Ref<number> = ref(4);
  const totalPages: ComputedRef<number> = computed(() =>
    Math.ceil(invoices.value.length / columnsPerPage.value)
  );
  const currentPage: Ref<number> = ref(totalPages.value);
  const scrollContainer: Ref<HTMLElement | null> = ref(null);
  const currentInvoice: Ref<IInvoice | null> = ref(null);
  const errorMessage: Ref<string> = ref('');
  const language: ComputedRef<string | undefined> = computed(() => customersCareStore.getLanguage);

  watch(
    () => customersCareStore.invoicesByNif,
    (newInvoicesByNif: IInvoicesItem | null) => {
      if (!newInvoicesByNif?.invoices || newInvoicesByNif?.invoices.length === 0) {
        errorMessage.value = '';
        currentInvoice.value = null;
      } else {
        errorMessage.value = '';
        currentInvoice.value = newInvoicesByNif?.invoices[0];
        selectedInvoice.value = currentInvoice.value || null;
        isDetailInvoice.value = true;
      }
    },
    { immediate: true }
  );

  watch(invoices, (newInvoices: IInvoice[]) => {
    if (!newInvoices || newInvoices.length === 0) {
      errorMessage.value = '';
    } else {
      errorMessage.value = '';
    }
  });

  watchEffect(() => {
    if (invoices.value.length > 0) {
      currentInvoice.value =
        invoices.value.find(
          (invoice: any) => invoice.numFactura === selectedInvoice.value?.numFactura
        ) || null;
    }
  });

  // Detectar mida de la pantalla i ajustar les columnas
  const adjustColumnsPerPage = () => {
    const screenWidth: number = window.innerWidth;
    if (screenWidth >= 1040) {
      columnsPerPage.value = 12; // Mostrar 12 en desktop
    } else if (screenWidth >= 768) {
      columnsPerPage.value = 6; // Màxim 6 columnes en tablet
    } else {
      columnsPerPage.value = 4; // Màxim 4 columnes en mobile
    }
  };

  const getScrollPage = () => {
    if (scrollContainer.value) {
      const pageWidth: number = scrollContainer.value.offsetWidth;
      scrollContainer.value.scrollLeft = pageWidth * (currentPage.value - 1);
    }
  };
  const handleScroll = () => {
    if (scrollContainer.value) {
      const scrollPosition: number = scrollContainer.value?.scrollLeft;
      const pageWidth: number = scrollContainer.value.offsetWidth;
      currentPage.value = Math.ceil(scrollPosition / pageWidth) + 1;
    }
  };

  const currentInvoicePrice: ComputedRef<string> = computed(() =>
    currentInvoice.value ? currentInvoice.value.totFactura : '0.00'
  );
  const currentInvoiceDate: ComputedRef<string> = computed(() =>
    currentInvoice.value ? currentInvoice.value.fecEmision : '-'
  );
  const currentInvoicePaymentDate: ComputedRef<string> = computed(() =>
    currentInvoice.value ? currentInvoice.value.fecVencimiento : '-'
  );
  const currentInvoiceNumber: ComputedRef<string> = computed(() =>
    currentInvoice.value ? currentInvoice.value.numFactura : '-'
  );

  const formatDate = (date: string): string => {
    if (!date || date === '') return '';
    const month = getMonth(date);
    const year = getYear(date).slice(-2);
    return `${month.slice(0, 3)} ${year}`;
  };

  const getMonth = (date: string): string => {
    const month = new Intl.DateTimeFormat(language.value, { month: 'long' }).format(new Date(date));
    return month.charAt(0).toUpperCase() + month.slice(1);
  };

  const getMonthNumber = (date: string | Date): number => {
    const newDate = new Date(date);
    return newDate.getMonth() + 1;
  };

  const getYear = (date: string): string => {
    return new Date(date).getFullYear().toString();
  };

  const goToPage = (pageIndex: number): void => {
    currentPage.value = pageIndex;
    getScrollPage();
  };

  // Obtenir les columnas visibles segons la pàgina
  const visibleInvoices: ComputedRef<IInvoice[][]> = computed(() => {
    const visibleInvoices: IInvoice[][] = [];
    for (let currentPage = 0; currentPage < totalPages.value; currentPage++) {
      const start = currentPage * columnsPerPage.value;
      const invoicesSubset: IInvoice[] = invoices.value.slice(start, start + columnsPerPage.value);

      if (invoicesSubset.length < columnsPerPage.value) {
        const repeatCount: number = columnsPerPage.value - invoicesSubset.length;
        for (let i = 0; i < repeatCount; i++) {
          invoicesSubset.unshift({
            numFactura: '',
            fecEmision: '',
            fecVencimiento: '',
            enviadaCobros: '0',
            totFactura: '0'
          });
        }
      }
      visibleInvoices.unshift(
        invoicesSubset.sort(
          (invoiceA: IInvoice, invoiceB: IInvoice) =>
            new Date(invoiceA.fecEmision).getTime() - new Date(invoiceB.fecEmision).getTime()
        )
      );
    }
    return visibleInvoices;
  });

  // Pass for download selected invoice
  /* const formatPeriod = (fecEmision: string) => {
  const [year, month] = fecEmision.split('-')
  return `${year}-${month}`
} */

  const goInvoiceView = (invoiceNumber: string): void => {
    const period: string = currentInvoiceDate.value.slice(0, 7);
    localStorage.setItem('selectedInvoicePeriod', `${period}`);

    router.push({
      name: 'Invoice',
      params: { id: invoiceNumber }
    });
  };

  // Gràfic
  const chartData = (visibleInvoices: IInvoice[]) => {
    return {
      labels: visibleInvoices.map((invoice: IInvoice) => `${formatDate(invoice.fecEmision)}`),
      datasets: [
        {
          label: t(`billing.invoice.invoice-price`),
          backgroundColor: visibleInvoices.map((invoice: IInvoice) =>
            selectedInvoice.value?.numFactura === invoice.numFactura
              ? `rgb(${getCssVaribleValue('--color-primary')})`
              : '#c1c1c1'
          ),
          data: visibleInvoices.map((invoice: IInvoice) => parseFloat(invoice.totFactura)),
          borderRadius: {
            topLeft: 5,
            topRight: 5
          }
        }
      ]
    };
  };

  const chartOptions = (visibleInvoice: IInvoice[], pageNumber: number) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      onHover: (event: any, chartElements: any) => {
        event.native.target.style.cursor = chartElements.length ? 'pointer' : 'default';
      },
      barPercentage: 1,
      categoryPercentage: 0.4,
      scales: {
        y: {
          display: false,
          grid: {
            display: false
          },
          ticks: {
            display: false
          }
        },
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 16
            }
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            title: (tooltipItems: any) => {
              const invoice: IInvoice = visibleInvoice[tooltipItems[0].dataIndex];
              return `${getMonth(invoice.fecEmision)} ${getYear(invoice.fecEmision)}`;
            },
            label: (tooltipItems: any) => {
              const value = tooltipItems.raw;
              return `Cost: ${value} €`;
            }
          },
          bodyFont: {
            size: 16
          },
          titleFont: {
            size: 16
          }
        }
      },
      layout: {
        padding: {
          left: 0
        }
      },
      onClick: (event: any, chart: any) => handleChartClick(event, pageNumber)
    };
  };

  //clic al gràfic
  const handleChartClick = (event: any, pageNumber: number) => {
    const chart = event.chart;

    const elements = chart.getElementsAtEventForMode(
      event.native,
      'nearest',
      { intersect: true },
      true
    );

    if (elements.length > 0) {
      const index: number = elements[0].index;
      selectedInvoice.value = visibleInvoices.value[pageNumber][index];
      isDetailInvoice.value = true;
    }
  };

  onMounted(async () => {
    window.addEventListener('resize', adjustColumnsPerPage);
    adjustColumnsPerPage();
    await nextTick();
    if (invoices.value.length > 0) {
      setInvoiceData();
    }
  });

  const setInvoiceData = async () => {
    const firstInvoice = invoices.value[0];
    selectedInvoice.value = firstInvoice || null;
    isDetailInvoice.value = true;
    currentPage.value = totalPages.value;
    await nextTick();

    if (scrollContainer.value) {
      getScrollPage();
    }
  };

  watch(
    invoices,
    (newInvoices) => {
      if (newInvoices.length > 0) {
        setInvoiceData();
      }
    },
    { immediate: true }
  );

  watch(
    () => scrollContainer.value,
    (newValue) => {
      if (newValue) {
        getScrollPage();
      }
    },
    { immediate: true }
  );
</script>
