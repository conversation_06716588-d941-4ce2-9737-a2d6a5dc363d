<template>
  <div
    v-if="productType === 'Mobile'"
    class="grid grid-cols-3 gap-2 mt-0 mb-2 overflow-x-auto whitespace-nowrap"
  >
    <div
      v-for="(tab, index) in tabs"
      :key="index"
      @click="setActiveTab(tab.value)"
      :class="activeTab === tab.value ? 'font-bold text-primary' : 'text-gray dark:text-gray-light'"
      class="flex tab-item justify-center bg-white dark:bg-dark-background rounded-lg cursor-pointer px-4 py-2 text-md flex-shrink-0"
    >
      {{ tab.label }}
    </div>
  </div>

  <WhiteCardComponent class="flex flex-col w-full pb-1">
    <div class="flex justify-between px-2"></div>
    <div class="mt-2 mb-10 flex flex-col items-center justify-center">
      <div class="flex items-center justify-center gap-3">
        <div v-for="(legendItem, index) in legendData" :key="index" class="flex items-center gap-1">
          <span :style="{ backgroundColor: legendItem.color }" class="w-5 h-5 rounded-full"></span>
          <p class="text-md">{{ legendItem.label }}</p>
        </div>
      </div>
    </div>
    <div
      ref="scrollContainer"
      class="flex overflow-y-hidden overflow-x-auto h-[250px] md:h-[350px] pb-2"
      @scroll="handleScroll"
    >
      <Bar
        v-show="activeTab === 'All' && productType?.toLowerCase() === 'mobile'"
        v-for="(visibleProductData, index) in visibleData"
        :key="index"
        :data="combinedChart(visibleProductData, index)"
        :options="combinedChartOptions(index)"
      />
      <Bar
        v-show="activeTab === 'Data' && productType?.toLowerCase() === 'mobile'"
        v-for="(visibleProductData, index) in visibleData"
        :key="index"
        :data="dataChart(visibleProductData, index)"
        :options="dataChartOptions(index)"
      />
      <Bar
        v-show="activeTab === 'Calls'"
        v-for="(visibleProductData, index) in visibleData"
        :key="index"
        :data="callsChart(visibleProductData, index)"
        :options="callsChartOptions(index)"
      />
    </div>
    <div class="flex justify-center gap-3 my-3" v-if="totalPages > 1">
      <div
        v-for="(dot, index) in totalPages"
        :key="index"
        :class="['w-3 h-3 rounded-full', currentPage === index + 1 ? 'bg-primary' : 'bg-gray']"
        @click="goToPage(index + 1)"
        class="cursor-pointer"
      ></div>
    </div>
  </WhiteCardComponent>
</template>

<script lang="ts">
  export default {
    name: 'ChartConsumptionComponent'
  };
</script>

<script setup lang="ts">
  import {
    ref,
    computed,
    onMounted,
    onUnmounted,
    watch,
    defineAsyncComponent,
    type ComputedRef,
    type Ref,
    type PropType
  } from 'vue';
  import { Bar } from 'vue-chartjs';
  import {
    Chart as ChartJS,
    Title,
    Tooltip,
    BarElement,
    CategoryScale,
    LinearScale,
    type ChartData
  } from 'chart.js';
  import { useI18n } from 'vue-i18n';
  import { getCssVaribleValue } from '@/utils/colors/getCssVaribleValue';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  import { useCustomersCare } from '@/stores/customersCare';
  import type { IChartDataValue } from '@/components/ProductInfoComponent/utils/chart-data-value.interface';
  import type { IVisibleData } from '@/components/ChartConsumptionComponent/utils/visible-data.interface';

  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  ChartJS.register(Title, Tooltip, BarElement, CategoryScale, LinearScale);

  const { t } = useI18n();

  const props = defineProps({
    calls: { type: Array as PropType<IChartDataValue[]>, required: true },
    callsLandline: { type: Array as PropType<IChartDataValue[]>, required: true },
    callsMobile: { type: Array as PropType<IChartDataValue[]>, required: true },
    dataGb: { type: Array as PropType<IChartDataValue[]>, required: false },
    monthLabels: { type: Array as PropType<string[]>, required: true },
    productType: { type: String, required: false }
  });
  watch(
    () => props.monthLabels,
    async () => {
      selectedIndex.value = props.monthLabels.length - 1;
    }
  );

  const customersCareStore: any = useCustomersCare();
  const emit = defineEmits(['updateSelectedMonth']);
  const selectedIndex = ref(props.monthLabels.length - 1);
  const activeTab = ref(props.productType?.toLocaleLowerCase() === 'landline' ? 'Calls' : 'All');
  const columnsPerPage = ref(6);
  const totalPages = computed(() => Math.ceil(props.monthLabels.length / columnsPerPage.value));
  const currentPage = ref(totalPages.value);
  const scrollContainer = ref<HTMLElement | null>(null);
  const savedSelectedProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.selectedProduct
  );
  const selectedProduct: Ref<IPrimaryProduct | null> = ref(null);
  const language: ComputedRef<string | undefined> = computed(() => customersCareStore.getLanguage);
  const themeMode: string | null = localStorage.getItem('theme');
  const isDarkMode: Ref<boolean> = ref(themeMode === 'dark');
  const currentMonth = ref(new Date().getMonth() + 1);
  const tabs: { value: string; label: string }[] = [
    { value: 'All', label: t('products.consumption.see-all') },
    { value: 'Data', label: t('products.consumption.see-data') },
    { value: 'Calls', label: t('products.consumption.see-calls') }
  ];

  const maxYScale = (property: IChartDataValue[]): number => {
    if (!property) return 0;
    const arrayOfValues: number[] = property.map((item) => parseFloat(item.y));
    return Math.max(...arrayOfValues);
  };

  const legendData = ref([
    {
      label: t('products.consumption.diagram-chartBill-legend-data'),
      color: isDarkMode.value ? '#f6f6f6' : `rgb(${getCssVaribleValue('--color-secondary')})`
    },
    {
      label: t('products.consumption.diagram-chartBill-legend-calls'),
      color: `rgb(${getCssVaribleValue('--color-primary')})`
    }
  ]);

  const getSelectedProduct = () => {
    selectedProduct.value = savedSelectedProduct.value
      ? savedSelectedProduct.value
      : localStorage.getItem('selectedProduct')
        ? JSON.parse(localStorage.getItem('selectedProduct') || '')
        : null;
  };

  const adjustColumnsPerPage = () => {
    const screenWidth = window.innerWidth;
    if (screenWidth >= 1040) {
      columnsPerPage.value = props.monthLabels.length;
    } else {
      if (activeTab.value === 'All') {
        columnsPerPage.value =
          props.monthLabels.length >= 4
            ? props.monthLabels.length / Math.ceil(props.monthLabels.length / 4)
            : 4;
      } else {
        columnsPerPage.value =
          props.monthLabels.length >= 6
            ? props.monthLabels.length / Math.ceil(props.monthLabels.length / 6)
            : 6;
      }
    }
  };

  watch(
    () => props.productType,
    (newProductType) => {
      if (newProductType?.toLocaleLowerCase() === 'landline') {
        activeTab.value = 'Calls';
      } else {
        activeTab.value = 'All';
      }
    },
    { immediate: true }
  );

  watch(
    () => activeTab.value,
    (newTab) => {
      adjustColumnsPerPage();
    }
  );

  watch(
    () => savedSelectedProduct,
    (newProduct) => {
      getSelectedProduct();
    },
    { deep: true }
  );

  onMounted(() => {
    setActiveTab(activeTab.value);
    window.addEventListener('resize', adjustColumnsPerPage);
    adjustColumnsPerPage();
    currentPage.value = totalPages.value;
    getScrollPage();
  });

  onUnmounted(() => {
    window.removeEventListener('resize', adjustColumnsPerPage);
    selectedIndex.value = props.monthLabels.length - 1;
  });

  const getScrollPage = () => {
    if (scrollContainer.value) {
      const pageWidth = scrollContainer.value.offsetWidth;
      scrollContainer.value.scrollLeft = pageWidth * (currentPage.value - 1);
    }
  };
  const handleScroll = () => {
    if (scrollContainer.value) {
      const scrollPosition = scrollContainer.value?.scrollLeft;
      const pageWidth = scrollContainer.value.offsetWidth;
      currentPage.value = Math.ceil(scrollPosition / pageWidth) + 1;
    }
  };

  const visibleData = computed((): IVisibleData[] => {
    const visibleData: IVisibleData[] = [];
    for (let currentPage = 0; currentPage < totalPages.value; currentPage++) {
      const start = currentPage * columnsPerPage.value;
      const end = start + columnsPerPage.value;
      visibleData.push({
        dataGb: Array.isArray(props.dataGb) ? props.dataGb.slice(start, end) : [],
        calls: props.calls.slice(start, end),
        callsLandline: props.callsLandline.slice(start, end),
        callsMobile: props.callsMobile.slice(start, end),
        labels: props.monthLabels.slice(start, end)
      });
    }

    return visibleData;
  });

  const goToPage = (pageIndex: number) => {
    currentPage.value = pageIndex;
    getScrollPage();
  };

  const formatDecimalHours = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours}h ${minutes}m ${seconds}s`;
  };

  const dataChart = (
    visibleData: IVisibleData,
    pageNumber: number
  ): ChartData<'bar', { x: number; y: string }[]> => {
    return {
      labels: visibleData.labels,
      datasets: [
        {
          label: 'GB',
          backgroundColor: visibleData.dataGb.map((_: any, index: any) =>
            Math.floor(index + columnsPerPage.value * pageNumber) === selectedIndex.value
              ? isDarkMode.value
                ? '#f6f6f6'
                : `rgb(${getCssVaribleValue('--color-secondary')})`
              : isDarkMode.value
                ? '#757b80'
                : '#c1c1c1'
          ),
          data: visibleData.dataGb.map((item: any) => ({ x: item.x, y: item.y })),
          borderRadius: { topLeft: 5, topRight: 5 }
        }
      ]
    };
  };

  const callsChart = (
    visibleData: IVisibleData,
    pageNumber: number
  ): ChartData<'bar', { x: number; y: string }[]> => {
    return {
      labels: visibleData.labels,
      datasets: [
        /*      {
          label: t('products.consumption.diagram-chartBill-legend-calls'),
          backgroundColor: visibleData.callsLandline.map((_: any, index: any) =>
            Math.floor(index + columnsPerPage.value * pageNumber) === selectedIndex.value
              ? `rgb(${getCssVaribleValue('--color-primary')})`
              : isDarkMode.value
                ? '#757b80'
                : '#c1c1c1'
          ),
          data: visibleData.callsLandline.map((item: any) => ({ x: item.x, y: item.y })),
          borderRadius: { topLeft: 0, topRight: 0 }
        }, */
        {
          label: 'Mobile',
          backgroundColor: visibleData.callsMobile.map((_: any, index: any) =>
            Math.floor(index + columnsPerPage.value * pageNumber) === selectedIndex.value
              ? `rgb(${getCssVaribleValue('--color-primary')})`
              : isDarkMode.value
                ? '#50575c'
                : '#e0e0e0'
          ),
          data: visibleData.callsMobile,
          borderRadius: { topLeft: 5, topRight: 5 }
        }
      ]
    };
  };

  const combinedChart = (
    visibleData: IVisibleData,
    pageNumber: number
  ): ChartData<'bar', { x: number; y: string }[]> => {
    return {
      labels: visibleData.labels,
      datasets: [
        {
          label: t('products.consumption.diagram-chartBill-legend-data'),
          backgroundColor: visibleData.dataGb.map((_: any, index: any) =>
            Math.floor(index + columnsPerPage.value * pageNumber) === selectedIndex.value
              ? isDarkMode.value
                ? '#f6f6f6'
                : `rgb(${getCssVaribleValue('--color-secondary')})`
              : isDarkMode.value
                ? '#757b80' //'#5B5F62'
                : '#c1c1c1'
          ),
          data: visibleData.dataGb.map((item: any) => ({ x: item.x, y: item.y })),
          borderRadius: { topLeft: 5, topRight: 5 },
          yAxisID: 'y-data'
        },
        {
          label: t('products.consumption.diagram-chartBill-legend-calls'),
          backgroundColor: visibleData.calls.map((_: IChartDataValue, index: any) =>
            Math.floor(index + columnsPerPage.value * pageNumber) === selectedIndex.value
              ? `rgb(${getCssVaribleValue('--color-primary')})`
              : isDarkMode.value
                ? '#50575c'
                : '#e0e0e0'
          ),
          data: visibleData.calls.map((item: any) => ({ x: item.x, y: item.y })),
          borderRadius: { topLeft: 5, topRight: 5 },
          yAxisID: 'y-calls'
        }
      ]
    };
  };

  const dataChartOptions: any = (pageNumber: number) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      onHover: (event: any, chartElements: any) => {
        event.native.target.style.cursor = chartElements.length ? 'pointer' : 'default';
      },

      plugins: {
        legend: {
          // EN FALSE
          display: false,
          labels: {
            usePointStyle: true,
            pointStyle: 'circle',
            align: 'end',
            font: {
              size: 14
            },
            generateLabels: function (chart: any) {
              const labels = chart.data.datasets.map((dataset: any, index: any) => {
                return {
                  text: dataset.label,
                  fillStyle: isDarkMode.value ? '#f6f6f6' : 'rgb(var(--color-secondary))',
                  strokeStyle: '#fff',
                  pointStyle: 'circle',
                  index: index
                };
              });
              return labels;
            }
          }
        },
        tooltip: {
          callbacks: {
            title: function (tooltipItems: any) {
              const month = tooltipItems[0].raw.x;
              const label = tooltipItems[0].label;
              return formatMonth(month, label);
            },
            label: function (tooltipItem: any) {
              const value = tooltipItem.raw.y;
              return parseFloat(value).toFixed(0) !== '0'
                ? `GB: ${parseFloat(value)?.toFixed(2)}`
                : `MB: ${(parseFloat(value) * 1024)?.toFixed(0)}`;
            }
          },
          bodyFont: {
            size: 16
          },
          titleFont: {
            size: 16
          }
        }
      },
      scales: {
        y: {
          display: false,
          grid: { display: false },
          beginAtZero: true,
          max: props.dataGb ? maxYScale(props.dataGb) : 0
        },
        x: {
          grid: { display: false },
          ticks: {
            font: {
              size: 16
            }
          }
        }
      },
      barPercentage: 1,
      categoryPercentage: 0.4,
      onClick: (event: any, chart: any) => handleChartClick(event, pageNumber)
    };
  };

  const callsChartOptions: any = (pageNumber: number) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      onHover: (event: any, chartElements: any) => {
        event.native.target.style.cursor = chartElements.length ? 'pointer' : 'default';
      },
      plugins: {
        legend: {
          // EN FALSE
          display: false,
          labels: {
            usePointStyle: true,
            pointStyle: 'circle',
            align: 'end',
            font: {
              size: 14
            },
            generateLabels: function (chart: any) {
              const labels = chart.data.datasets.map((dataset: any, index: any) => {
                return {
                  text: dataset.label,
                  fillStyle:
                    index === 0
                      ? `rgb(${getCssVaribleValue('--color-primary')})`
                      : `rgb(${getCssVaribleValue('--color-primary-medium')})`,
                  strokeStyle: '#fff',
                  pointStyle: 'circle',
                  index: index
                };
              });
              return labels;
            }
          }
        },
        tooltip: {
          callbacks: {
            title: function (tooltipItems: any) {
              const month = tooltipItems[0].raw.x;
              const label = tooltipItems[0].label;
              return formatMonth(month, label);
            },
            label: function (tooltipItem: any) {
              const value = tooltipItem.raw.y;
              return `${t('products.consumption.diagram-chartBill-legend-calls')}: ${formatDecimalHours(value.toString())}`;
              /*               const datasetLabel = tooltipItem.dataset.label.toLowerCase();
               */

              /*  if (datasetLabel.includes('landline') && tooltipItem.raw.x !== currentMonth.value) {
                return `${t('products.consumption.diagram-chartBill-legend-callsLandline')}: ${hours}h ${minutes}m ${seconds}s`;
              } else {
                return `${t('products.consumption.diagram-chartBill-legend-calls')}: ${hours}h ${minutes}m ${seconds}s`;
              } */
            }
          },
          bodyFont: {
            size: 16
          },
          titleFont: {
            size: 16
          }
        }
      },
      scales: {
        y: {
          display: false,
          stacked: true,
          grid: { display: false },
          beginAtZero: true,
          max: maxYScale(props?.calls)
        },
        x: {
          stacked: true,
          grid: { display: false },
          ticks: {
            font: {
              size: 16
            }
          }
        }
      },
      barPercentage: 1,
      categoryPercentage: 0.4,
      onClick: (event: any, chart: any) => handleChartClick(event, pageNumber)
    };
  };

  const combinedChartOptions: any = (pageNumber: number) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      onHover: (event: any, chartElements: any) => {
        event.native.target.style.cursor = chartElements.length ? 'pointer' : 'default';
      },
      plugins: {
        legend: {
          // EN FALSE
          display: false,
          labels: {
            usePointStyle: true,
            pointStyle: 'circle',
            align: 'end',
            font: {
              size: 14
            },
            generateLabels: function (chart: any) {
              const labels = chart.data.datasets.map((dataset: any, index: any) => {
                return {
                  text: dataset.label,
                  fillStyle:
                    dataset.label === t('products.consumption.diagram-chartBill-legend-data')
                      ? isDarkMode.value
                        ? '#f6f6f6'
                        : `rgb(${getCssVaribleValue('--color-secondary')})`
                      : `rgb(${getCssVaribleValue('--color-primary')})`,
                  strokeStyle: '#fff',
                  pointStyle: 'circle',
                  index: index
                };
              });
              return labels;
            }
          }
        },
        tooltip: {
          callbacks: {
            title: function (tooltipItems: any) {
              const month = tooltipItems[0].raw.x;
              const label = tooltipItems[0].label;
              return formatMonth(month, label);
            },
            label: function (tooltipItem: any) {
              const datasetIndex = tooltipItem.datasetIndex;
              const value = tooltipItem.raw.y;

              if (datasetIndex === 0) {
                return parseFloat(value).toFixed(0) !== '0'
                  ? `GB: ${parseFloat(value).toFixed(2)}`
                  : `MB: ${(parseFloat(value) * 1024).toFixed(0)}`;
              } else if (datasetIndex === 1) {
                return `${t('products.minuts')}: ${formatDecimalHours(value.toString())}`;
              }
            }
          },
          bodyFont: {
            size: 16
          },
          titleFont: {
            size: 16
          }
        }
      },
      scales: {
        'y-calls': {
          display: false,
          grid: { display: false },
          beginAtZero: true,
          max: props.calls ? maxYScale(props.calls) : 0
        },
        'y-data': {
          display: false,
          grid: { display: false },
          beginAtZero: true,
          max: props.dataGb ? maxYScale(props.dataGb) : 0
        },
        x: {
          grid: { display: false },
          ticks: {
            font: {
              size: 16
            }
          }
        }
      },
      layout: { padding: { left: 0 } },
      onClick: (event: any, chart: any) => handleChartClick(event, pageNumber)
    };
  };

  const handleChartClick = (event: any, pageNumber: number) => {
    const chart = event.chart;
    const elements = chart.getElementsAtEventForMode(
      event.native,
      'nearest',
      { intersect: true },
      true
    );
    if (elements.length > 0) {
      const clickedIndex = elements[0].index;
      const globalIndex = Math.floor(clickedIndex + pageNumber * columnsPerPage.value);
      selectedIndex.value = globalIndex;
      emit('updateSelectedMonth', globalIndex);
    }
  };

  const formatMonth = (month: number, label: string) => {
    const [monthAbbreviation, year] = label.split(' ');
    const date = new Date(+`20${year}`, month - 1).toLocaleString(language.value, {
      year: 'numeric',
      month: 'long'
    });
    return date.charAt(0).toUpperCase() + date.slice(1);
  };

  const setActiveTab = (tab: string) => {
    activeTab.value = tab;
    legendData.value =
      tab === 'Data' && props.productType?.toLocaleLowerCase() === 'mobile'
        ? [
            {
              label: t('products.consumption.diagram-chartBill-legend-data'),
              color: isDarkMode.value
                ? '#f6f6f6'
                : `rgb(${getCssVaribleValue('--color-secondary')})`
            }
          ]
        : tab === 'Calls' || props.productType?.toLocaleLowerCase() === 'landline'
          ? [
              {
                label: t('products.consumption.diagram-chartBill-legend-calls'),
                color: `rgb(${getCssVaribleValue('--color-primary')})`
              }
              /*      {
                label: t('products.consumption.diagram-chartBill-legend-calls'),
                color: `rgb(${getCssVaribleValue('--color-primary-medium')})`
              } */
            ]
          : [
              {
                label: t('products.consumption.diagram-chartBill-legend-data'),
                color: isDarkMode.value
                  ? '#f6f6f6'
                  : `rgb(${getCssVaribleValue('--color-secondary')})`
              },
              {
                label: t('products.consumption.diagram-chartBill-legend-calls'),
                color: `rgb(${getCssVaribleValue('--color-primary')})`
              }
            ];
  };

  watch(
    () => scrollContainer.value,
    (newValue) => {
      if (newValue) {
        getScrollPage();
      }
    },
    { immediate: true }
  );
</script>

<style scoped>
  .tab-item {
    flex-grow: 1;
    min-width: 100px;
    text-align: center;
  }

  @media (min-width: 768px) {
    .tab-item {
      min-width: 120px;
    }
  }
</style>
