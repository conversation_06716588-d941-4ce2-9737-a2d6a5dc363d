<template>
  <div
    v-if="pdfUrl"
    class="text-white fixed rounded-full right-2 lg:right-4 bottom-52 lg:bottom-44 size-11 lg:size-14 z-10 flex justify-center items-center"
    :class="pdf_scale >= maxZoom ? 'cursor-not-allowed bg-black/50' : 'cursor-pointer bg-black/80'"
    @click="zoomIn()"
  >
    <font-awesome-icon :icon="'fa-magnifying-glass-plus'" class="size-5 lg:size-6" :class="''" />
  </div>
  <div
    v-if="pdfUrl"
    class="text-white fixed rounded-full right-2 lg:right-4 bottom-40 lg:bottom-28 size-11 lg:size-14 z-10 flex justify-center items-center"
    :class="
      pdf_scale <= initial_scale ? 'cursor-not-allowed bg-black/50' : 'cursor-pointer bg-black/80'
    "
    @click="zoomOut()"
  >
    <font-awesome-icon :icon="'fa-magnifying-glass-minus'" class="size-5 lg:size-6" :class="''" />
  </div>
  <div></div>
  <div
    id="pdfViewer"
    class="max-h-[calc(100vh-56px-24px)] overflow-scroll w-[100wv-4px] bg-gray-200 xs:mt-2 lg:mt-12 -ml-2 -mr-4 pb-20 lg:pb-0 grab-container"
  >
    <div>
      <div :style="{ width: pdf_div_width, margin: '0 auto' }">
        <canvas v-for="page in pdf_pages" :id="'the_canvas' + page" :key="page"></canvas>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import * as PDFJS from 'pdfjs-dist';

  PDFJS.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@4.10.38/build/pdf.worker.min.mjs`;
  export default defineComponent({
    name: 'PdfViewer',
    props: {
      pdfUrl: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        pdf_scale: 1.0,
        initial_scale: 1.0,
        pdf_pages: 0,
        pdf_div_width: '',
        pdf_src: null,
        maxZoom: 4.0
      };
    },
    mounted() {
      this.get_pdfurl();
      this.enableGrabScroll();
    },
    methods: {
      zoomIn() {
        if (this.pdf_scale >= this.maxZoom) {
          return;
        }
        this.pdf_scale += 0.2;
        this._loadFile(this.pdfUrl);
      },
      zoomOut() {
        if (this.pdf_scale <= this.initial_scale) {
          return;
        }
        this.pdf_scale -= 0.2;
        this._loadFile(this.pdfUrl);
      },
      get_pdfurl() {
        this._loadFile(this.pdfUrl);
        return;
      },
      _loadFile(url: string) {
        let loadingTask = PDFJS.getDocument(url);
        loadingTask.promise.then((pdf: any) => {
          this.pdf_pages = pdf.numPages;
          this.$nextTick(() => {
            this.renderPage(1, pdf);
          });
        });
      },
      renderPage(num: number, pdfDoc: any) {
        pdfDoc.getPage(num).then((page: any) => {
          const canvas: any = document.getElementById('the_canvas' + num);
          const ctx = canvas.getContext('2d');
          const dpr = window.devicePixelRatio || 1;
          const bsr =
            ctx.webkitBackingStorePixelRatio ||
            ctx.mozBackingStorePixelRatio ||
            ctx.msBackingStorePixelRatio ||
            ctx.oBackingStorePixelRatio ||
            ctx.backingStorePixelRatio ||
            1;
          const ratio = dpr / bsr;

          if (num === 1 && this.pdf_scale === 1.0) {
            const viewport = page.getViewport({ scale: 1.0 });
            const screenWidth = window.innerWidth > 1040 ? 1040 : window.innerWidth - 24;
            this.initial_scale = screenWidth / viewport.width;
            this.pdf_scale = this.initial_scale; // Establir escala inicial
          }

          const viewport = page.getViewport({ scale: this.pdf_scale });

          canvas.width = viewport.width * ratio;
          canvas.height = viewport.height * ratio;
          canvas.style.width = `${viewport.width}px`;
          canvas.style.height = `${viewport.height}px`;
          this.pdf_div_width = `${viewport.width}px`;

          ctx.setTransform(ratio, 0, 0, ratio, 0, 0);

          const renderContext = {
            canvasContext: ctx,
            viewport: viewport
          };
          page.render(renderContext);
          if (this.pdf_pages > num) {
            this.renderPage(num + 1, pdfDoc);
          }
        });
      },
      enableGrabScroll() {
        const container = document.getElementById('pdfViewer');
        if (!container) return;

        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let scrollLeft = 0;
        let scrollTop = 0;

        container.addEventListener('mousedown', (e) => {
          isDragging = true;
          container.classList.add('grabbing');
          startX = e.pageX - container.offsetLeft;
          startY = e.pageY - container.offsetTop;
          scrollLeft = container.scrollLeft;
          scrollTop = container.scrollTop;
        });

        container.addEventListener('mousemove', (e) => {
          if (!isDragging) return;
          e.preventDefault();
          const x = e.pageX - container.offsetLeft;
          const y = e.pageY - container.offsetTop;
          const walkX = (x - startX) * 1; // Ajusta la velocitat d'arrossegament si cal
          const walkY = (y - startY) * 1;
          container.scrollLeft = scrollLeft - walkX;
          container.scrollTop = scrollTop - walkY;
        });

        container.addEventListener('mouseup', () => {
          isDragging = false;
          container.classList.remove('grabbing');
        });

        container.addEventListener('mouseleave', () => {
          isDragging = false;
          container.classList.remove('grabbing');
        });
      }
    }
  });
</script>
<style>
  .grab-container {
    cursor: grab;
    overflow: auto;
  }
  .grab-container.grabbing {
    cursor: grabbing;
  }
</style>
