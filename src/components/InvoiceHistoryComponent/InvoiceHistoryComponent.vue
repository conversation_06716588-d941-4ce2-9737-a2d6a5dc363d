<template>
  <p class="font-bold text-xl mt-5">{{ $t(`billing.invoice.invoice-history`) }}</p>
  <div v-if="errorMessage" class="flex flex-col items-center justify-center py-20 px-4">
    <p class="font-bold text-lg text-error">{{ errorMessage }}</p>
  </div>

  <WhiteCardComponent v-else class="mt-2">
    <ul>
      <li v-for="(invoice, index) in visibleInvoices" :key="index" class="flex flex-col">
        <div class="flex justify-between">
          <div class="w-full flex">
            <div class="w-10 flex justify-center items-center">
              <font-awesome-icon
                icon="fa-regular fa-file-lines"
                class="size-[26px] text-gray dark:text-gray-dark"
              ></font-awesome-icon>
            </div>
            <div class="pl-2">
              <!-- <PERSON>rar Mes y Año -->
              <p class="font-medium mb-0 text-gray dark:text-gray-dark mb-1">
                {{ getMonth(invoice.fecEmision) }}
                {{ getYear(invoice.fecEmision) }}
              </p>
              <p class="text-xl text-black -mt-1 dark:text-white">{{ invoice.totFactura }} €</p>
            </div>
          </div>
          <div class="flex">
            <div
              class="flex justify-center items-center mr-4"
              v-if="invoice.debt?.impDebe && invoice.debt?.impDebe"
            >
              <PwStatus
                v-if="getMonthNumber(invoice.fecEmision) < getMonthNumber(new Date())"
                :text-status="$t('billing.invoice.status-invoice-pending')"
                :style-status="'bg-error-light text-error text-center'"
                class="!text-lg !w-24"
              />
              <PwStatus
                v-if="getMonthNumber(invoice.fecEmision) === getMonthNumber(new Date())"
                :text-status="$t('billing.invoice.status-invoice-current-month')"
                :style-status="'bg-blue-light text-blue text-center'"
                class="!text-lg !w-24"
              />
            </div>
            <div class="flex justify-center items-center cursor-pointer mr-2">
              <font-awesome-icon
                icon="fa-regular fa-circle-down"
                class="size-[32px] text-gray dark:text-gray-dark"
                @click="downloadInvoice(invoice.fecEmision, invoice.numFactura)"
                title="$t('billing.invoice.download-invoice-title')"
              ></font-awesome-icon>
            </div>
          </div>
        </div>
        <div class="border-t border-gray-300 my-2 mx-2"></div>
      </li>
    </ul>

    <!-- Mostrar "Mostrar més" o "Mostrar menys" -->
    <div class="flex justify-center mt-2">
      <button
        v-if="visibleInvoices.length < invoices.length"
        @click="showMore"
        class="text-gray dark:text-gray-dark text-lg"
      >
        <font-awesome-icon
          icon="fa-chevron-down"
          class="size-[18px] text-gray dark:text-gray-dark mr-2"
          title="Mostrar més factures"
        ></font-awesome-icon>
        {{ $t(`billing.invoice.show-more`) }}
      </button>

      <button v-else @click="showLess" class="text-gray dark:text-gray-dark">
        <font-awesome-icon
          icon="fa-chevron-up"
          class="size-[18px] text-gray dark:text-gray-dark mr-2"
          title="Mostrar menys factures"
        ></font-awesome-icon>
        {{ $t(`billing.invoice.show-less`) }}
      </button>
    </div>
  </WhiteCardComponent>
</template>

<script setup lang="ts">
  import { defineAsyncComponent, ref, computed, type ComputedRef, watch } from 'vue';
  import type { Ref } from 'vue';
  import { PwStatus } from 'parlem-webcomponents-common';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useRouter } from 'vue-router';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  import type {
    IInvoice,
    IInvoicesItem
  } from '@/stores/customersCare/interfaces/invoice.interface';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const INITIAL_VISIBLE_COUNT: number = 5;
  const router = useRouter();
  const errorMessage: Ref<string> = ref('');

  const invoices: ComputedRef<IInvoice[]> = computed(
    () =>
      customersCareStore.invoicesByNif?.invoices
        ?.slice()
        .sort(
          (invoiceA: IInvoice, invoiceB: IInvoice) =>
            new Date(invoiceB.fecEmision).getTime() - new Date(invoiceA.fecEmision).getTime()
        ) || []
  );
  const visibleInvoicesCount: Ref<number> = ref(INITIAL_VISIBLE_COUNT);
  const visibleInvoices: ComputedRef<IInvoice[]> = computed(() =>
    invoices.value.slice(0, visibleInvoicesCount.value)
  );
  const language: ComputedRef<string | undefined> = computed(() => customersCareStore.getLanguage);

  watch(
    () => customersCareStore.invoicesByNif,
    (invoicesByNif: IInvoicesItem | null) => {
      if (!invoicesByNif?.invoices || invoicesByNif?.invoices.length === 0) {
        errorMessage.value = '';
      } else {
        errorMessage.value = '';
      }
    },
    { immediate: true }
  );

  watch(invoices, (newInvoices: IInvoice[]) => {
    if (!newInvoices || newInvoices.length === 0) {
      errorMessage.value = '';
    } else {
      errorMessage.value = '';
    }
  });

  const showMore = (): void => {
    visibleInvoicesCount.value += 6;
  };

  const showLess = (): void => {
    visibleInvoicesCount.value = INITIAL_VISIBLE_COUNT;
  };

  const formatPeriodo = (fecEmision: string): string => {
    const [year, month] = fecEmision.split('-');
    return `${year}-${month}`;
  };

  const downloadInvoice = (fecEmision: string, invoiceNumber: string): void => {
    const periodo = formatPeriodo(fecEmision);

    localStorage.setItem('selectedInvoicePeriod', `${periodo}`);

    router.push({
      name: 'Invoice',
      params: { id: invoiceNumber }
    });
  };
  const getMonth = (date: string): string => {
    const month = new Intl.DateTimeFormat(language.value, { month: 'long' }).format(new Date(date));
    return month.charAt(0).toUpperCase() + month.slice(1);
  };

  const getMonthNumber = (date: string | Date): number => {
    const newDate = new Date(date);
    return newDate.getMonth() + 1;
  };

  const getYear = (date: string): string => {
    return new Date(date).getFullYear().toString();
  };
</script>

<style scoped>
  .absolute {
    background-color: white;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    min-width: 150px;
  }
</style>
