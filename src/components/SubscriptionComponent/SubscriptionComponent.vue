<template>
  <WhiteCardComponent>
    <div class="flex items-center justify-between">
      <div>
        <p class="font-bold text-lg">{{ subscriptionText }}</p>
        <p>{{ subscriptionPrice }}€</p>
      </div>
      <div class="flex">
        <PwButton
          :text="$t('profile.list.createAction')"
          :theme="'primary-primary-light'"
          class="px-2 min-w-20 max-w-40 !m-0 max-h-8"
        ></PwButton>
      </div>
    </div>
  </WhiteCardComponent>
</template>

<script lang="ts">
  import { defineComponent, defineAsyncComponent } from 'vue';
  export default defineComponent({
    name: 'SubscriptionComponent'
  });
</script>

<script setup lang="ts">
  import { PwButton } from 'parlem-webcomponents-common';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const props = defineProps({
    subscriptionText: { type: String, required: true },
    subscriptionPrice: { type: String, required: true }
  });
</script>
