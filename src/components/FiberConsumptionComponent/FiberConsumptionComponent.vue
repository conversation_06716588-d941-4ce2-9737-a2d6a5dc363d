<template>
  <WhiteCardComponent class="flex justify-between items-start p-3">
    <div v-if="selectedProduct?.fiber" class="flex flex-col justify-between">
      <div v-if="selectedProduct.fiber?.installationAddress" class="mb-4">
        <p class="font-bold text-lg mb-1">{{ t('products.fiber.address') }}</p>
        <div class="flex items-center mb-1">
          <font-awesome-icon icon="fa-location-dot" class="h-5 w-5 mr-2" />
          <p>
            {{ setAddressString(selectedProduct.fiber?.installationAddress) }}
          </p>
        </div>
      </div>
      <p class="font-bold text-lg mb-1">
        {{ t('products.fiber.speed') }} {{ selectedProduct?.fiber?.coverageType }}
      </p>
      <p class="text-3xl text-primary font-bold mt-1">
        <span class="text-6xl">{{ selectedProduct?.fiber?.specifications.mbpsSpeed }}</span>
        MB <span class="text-lg">{{ t('products.fiber.speed-end') }}</span>
      </p>
    </div>
    <div v-else class="flex items-center">
      <font-awesome-icon
        icon="fa-exclamation-triangle"
        class="size-[26px] text-gray dark:text-gray-dark p-1 mr-2"
      />
      <p class="text-lg/6">{{ t('products.no-data') }}</p>
    </div>
  </WhiteCardComponent>
</template>

<script lang="ts">
  export default {
    name: 'ConsumptionComponent'
  };
</script>

<script setup lang="ts">
  import { defineAsyncComponent, type PropType } from 'vue';
  import { useI18n } from 'vue-i18n';

  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  import { setAddressString } from 'parlem-webcomponents-common';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const props = defineProps({
    selectedProduct: Object as PropType<IPrimaryProduct>,
    provisioningClass: String
  });

  const { t } = useI18n();
</script>

<style scoped></style>
