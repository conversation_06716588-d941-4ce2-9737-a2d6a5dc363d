<template>
  <div id="pw-street-map"></div>
  <div v-if="isLoading" class="flex flex-col items-center">
    <PwLoading
      :company="company"
      loading-style="w-32 h-32"
      loading-image-style="w-[20px]"
      class="mt-8"
    >
    </PwLoading>
    <p class="font-bold mt-4 text-lg text-primary">{{ $t('general.loading') }}...</p>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  export default defineComponent({ name: 'PwStreetMap' });
</script>

<script setup lang="ts">
  import { ref, computed, type PropType, onMounted, type Ref, type ComputedRef } from 'vue';
  import { useCustomersCare } from '@/stores/customersCare';
  import type { ICoverage } from './interfaces/coverage.interface';
  import { setAddressToSend } from 'parlem-webcomponents-common';
  import createScript from '@/utils/createScript';
  import { PwLoading } from 'parlem-webcomponents-common';
  import { useAuthStore } from '@/stores/auth/index';

  const props = defineProps({
    address: {
      type: Object as PropType<ICoverage> | null,
      default: null
    },
    from: {
      type: String,
      default: ''
    }
  });

  const emit = defineEmits(['saveAddress', 'resetAddress']);

  const pwStreetMap: any = ref(null);
  let recoveryStreetMapData = ref({});
  const customersCareStore = useCustomersCare();
  const isLoading: Ref<boolean> = ref(false);
  const authStore: any = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const profileItemToEdit: ComputedRef<any> = computed(() => customersCareStore.profileItemToEdit);

  const language = computed(() => customersCareStore.getLanguage);
  const configuration = {
    language: language.value,
    back: 'false',
    from: props.from,
    submitButtonText: 'Guardar la nova adreça'
  };

  onMounted((): void => {
    createScript('VITE_COVERAGE_WC', 'coverage');
    loadCoverageComponent();
  });

  const loadCoverageComponent = (): void => {
    if (props.address && (props.address.gescal17 || props.address.gescal37)) {
      recoveryStreetMapData.value = recoveryStreetMapAddress(props.address);
    }

    let streetmap = document.createElement('pw-street-map');

    const configurationStringify = JSON.stringify(configuration);
    streetmap.setAttribute('config', configurationStringify);

    const dataStringify = JSON.stringify(recoveryStreetMapData.value || '{}');
    if (dataStringify !== '{}') {
      streetmap.setAttribute('data', dataStringify);
    }
    pwStreetMap.value = document.getElementById('pw-street-map');
    if (pwStreetMap.value) {
      pwStreetMap.value.appendChild(streetmap);
    }
  };

  function recoveryStreetMapAddress(address: ICoverage) {
    return address;
  }

  window.addEventListener('submit-coverage-event', (e: any) => {
    const address: ICoverage = {
      ...profileItemToEdit.value,
      ...setAddressToSend(e.detail as ICoverage)
    };
    address.isDefault = profileItemToEdit.value ? profileItemToEdit.value.isDefault : false;
    emit('saveAddress', address);
  });
</script>
f
