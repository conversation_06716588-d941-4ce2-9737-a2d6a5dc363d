<template>
  <div
    ref="scrollContainerRef"
    class="flex gap-2 h-auto w-auto justify-start items-center overflow-x-auto"
  >
    <div
      v-for="(service, index) in services"
      :key="index"
      @click="selectService(service)"
      class="product-item pb-2 hover:font-semibold w-[95px] lg:w-[110px]"
      :class="{ 'text-primary': isSelected(service) }"
    >
      <div class="flex flex-col items-center gap-1 pt-4 cursor-pointer">
        <div
          class="flex rounded-full h-20 w-20 justify-center items-center bg-white dark:bg-dark-background shadow-md hover:dark:bg-dark-hover dark:shadow-black hover:dark:shadow-dark-light hover:shadow-gray/90 hover:bg-white/50"
        >
          <font-awesome-icon
            :icon="getIcon(service)"
            class="h-12 w-12"
            :class="isSelected(service) ? 'text-primary' : 'text-gray dark:text-gray-dark'"
          />
        </div>

        <p
          class="text-md truncate max-w-[90px] lg:max-w-[105px]"
          :class="isSelected(service) ? 'text-primary' : 'text-gray dark:text-gray-dark'"
          :title="getServiceDisplayName(service)"
        >
          {{ getServiceDisplayName(service) }}
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'StoryCard'
  };
</script>

<script setup lang="ts">
  import { ref, computed, watch, type Ref, type ComputedRef, onMounted } from 'vue';
  import { useCustomersCare } from '@/stores/customersCare';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  import type { IProducts } from '../../stores/customersCare/interfaces/products.interface';
  import type { IAuthStore } from '@/stores/auth/interfaces/store.interface';
  import { useAuthStore } from '@/stores/auth/index';

  const emit = defineEmits(['productIsLoading']);
  const customersCareStore: any = useCustomersCare();
  const authStore: IAuthStore = useAuthStore();
  const services: ComputedRef<IPrimaryProduct[]> = computed(() => customersCareStore.getServices);
  const products: ComputedRef<IProducts> = computed(() => customersCareStore.getProducts);
  const selectedService: Ref<IPrimaryProduct | null> = ref(null);
  const savedSelectedService: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.selectedService
  );
  const company: ComputedRef<string> = computed(() => authStore.company);

  onMounted(() => {
    if (services.value.length > 0) {
      initializeSelectedTel();
    }
  });

  const getIcon = (product: any) => {
    const provisioningClass = product.provisioningClass.toLowerCase();
    if (provisioningClass === 'fiber') {
      return 'wifi';
    } else if (provisioningClass === 'tv') {
      return 'tv';
    } else if (provisioningClass === 'mobile') {
      return 'mobile-screen-button';
    } else if (provisioningClass === 'landline') {
      return 'phone';
    } else {
      return 'minus';
    }
  };

  const getServiceDisplayName = (service: any) => {
    const provisioningClass = service.provisioningClass.toLowerCase();
    if (['fiber'].includes(provisioningClass)) {
      return 'Fibra';
    } else if (['mobile', 'landline'].includes(provisioningClass)) {
      return service.number;
    } else {
      return service.productName || '';
    }
  };

  const isSelected = (service: any) => {
    return selectedService.value?.id === service.id;
  };

  const getSelectedService = () => {
    selectedService.value = savedSelectedService.value
      ? savedSelectedService.value
      : localStorage.getItem('selectedService')
        ? JSON.parse(localStorage.getItem('selectedService') || '')
        : services.value.length
          ? services.value[0]
          : null;
  };

  const selectService = async (service: any) => {
    selectedService.value = service;
    customersCareStore.setSelectedService(service);
    localStorage.setItem('selectedService', JSON.stringify(service));
  };

  const initializeSelectedTel = () => {
    getSelectedService();
    if (selectedService.value) {
      selectService(selectedService.value);
    } else {
      selectService(services.value[0]);
    }
  };

  // scroll
  const scrollContainerRef = ref<HTMLElement | null>(null);

  const scrollToSelectedService = () => {
    const selectedIndex = services.value.findIndex(
      (product: any) => product.id === selectedService.value?.id
    );
    if (scrollContainerRef.value && selectedIndex !== -1) {
      const productWidth = 100;
      const containerWidth = scrollContainerRef.value.clientWidth;
      const scrollAmount = selectedIndex * productWidth - containerWidth / 2 + productWidth / 2;

      scrollContainerRef.value.scrollTo({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  watch(selectedService, async (newService: IPrimaryProduct | null) => {
    emit('productIsLoading', true);
    scrollToSelectedService();
    if (!newService) return;
    if (!products.value[newService.id]) {
      await customersCareStore.getPrimaryProductsByContractProductId(company.value, newService.id);
    }
    emit('productIsLoading', false);
  });

  watch(
    services,
    (newServices) => {
      if (newServices.length > 0) {
        initializeSelectedTel();
      }
    },
    { immediate: true }
  );
</script>

<style scoped></style>
