<template>
  <WhiteCardComponent>
    <div class="flex justify-between pb-3">
      <p class="font-bold">Additional costs</p>
      <p class="font-bold">{{ totalAdditionalCost }}€</p>
    </div>
    <div class="flex flex-col gap-1">
      <!-- SMS -->
      <details @toggle="isVisibleDetaisAdditionalCostSMS = !isVisibleDetaisAdditionalCostSMS">
        <summary class="flex justify-between">
          <div class="flex justify-start gap-2">
            <font-awesome-icon
              :icon="isVisibleDetaisAdditionalCostSMS ? 'fa-chevron-up' : 'fa-chevron-down'"
              class="h-5 w-5 text-gray dark:text-gray-dark"
            />

            <p>SMS</p>
          </div>
          <p>{{ totalSMSAdditionalCost }}€</p>
        </summary>

        <span class="flex flex-col ml-7 text-gray dark:text-gray-dark text-sm">
          <div class="flex justify-between">
            <p>Special SMS</p>
            <p>{{ specialSMS }}€</p>
          </div>

          <div class="flex justify-between">
            <p>International SMS</p>
            <p>{{ internationalSMS }}€</p>
          </div>
        </span>
      </details>

      <!-- CALLS -->
      <details @toggle="isVisibleDetaisAdditionalCostCalls = !isVisibleDetaisAdditionalCostCalls">
        <summary class="flex justify-between">
          <div class="flex justify-start gap-2">
            <font-awesome-icon
              :icon="isVisibleDetaisAdditionalCostCalls ? 'fa-chevron-up' : 'fa-chevron-down'"
              class="h-5 w-5 text-gray dark:text-gray-dark"
            />

            <p>Calls</p>
          </div>
          <p>{{ totalCallsAdditionalCost }}€</p>
        </summary>

        <span class="flex flex-col ml-7 text-gray dark:text-gray-dark text-sm">
          <div class="flex justify-between">
            <p>Special Calls</p>
            <p>{{ specialCalls }}€</p>
          </div>

          <div class="flex justify-between">
            <p>International Calls</p>
            <p>{{ internationalCalls }}€</p>
          </div>
        </span>
      </details>
    </div>
  </WhiteCardComponent>
</template>

<script lang="ts">
import { defineComponent, defineAsyncComponent } from 'vue'
const WhiteCardComponent = defineAsyncComponent(
  () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
)

export default defineComponent({
  name: 'AdditionalCostComponent'
})
</script>

<script setup lang="ts">
import { onMounted, ref, type Ref } from 'vue'

// const props = defineProps({
//   specialCalls: { type: Number, required: false },
//   internationalCalls: { type: Number, required: false },
//   specialSMS: { type: Number, required: false },
//   internationalSMS: { type: Number, required: false }
// })

const isVisibleDetaisAdditionalCostSMS = ref(false)
const isVisibleDetaisAdditionalCostCalls = ref(false)

const totalAdditionalCost = ref<number>(0)
const totalSMSAdditionalCost = ref<number>(0)
const totalCallsAdditionalCost = ref<number>(0)

const specialCalls = 12
const internationalCalls = 180

const specialSMS = 10
const internationalSMS = 18

const calculateSMS = (specialSMS: number, internationalSMS: number) => {
  return (totalSMSAdditionalCost.value = specialSMS + internationalSMS)
}

const calculateCalls = (specialCalls: number, internationalCalls: number) => {
  return (totalCallsAdditionalCost.value = specialCalls + internationalCalls)
}

const calculateTotalAdditionalCost = (
  totalSMSAdditionalCost: Ref<number>,
  totalCallsAdditionalCost: Ref<number>
) => {
  return (totalAdditionalCost.value = totalSMSAdditionalCost.value + totalCallsAdditionalCost.value)
}

onMounted(() => {
  calculateSMS(specialSMS, internationalSMS)
  calculateCalls(specialCalls, internationalCalls)
  calculateTotalAdditionalCost(totalSMSAdditionalCost, totalCallsAdditionalCost)
})
</script>
