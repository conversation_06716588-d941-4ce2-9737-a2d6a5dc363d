<template>
  <section
    class="mt-6"
    v-show="sectionFields && sectionFields.length"
    v-for="(section, index) of sectionFields"
    :key="index"
  >
    <p class="font-bold text-xl mb-3 dark:text-white">{{ $t(section?.title) }}</p>
    <WhiteCardComponent>
      <li
        v-show="section.fields"
        v-for="(field, fieldIndex) of section.fields"
        :key="fieldIndex"
        class="flex items-center"
        :class="{
          'border-b border-gray-light pb-2': fieldIndex !== section.fields.length - 1,
          'pt-2': fieldIndex !== 0
        }"
      >
        <div v-if="typeof field === 'object'" class="flex items-center w-full">
          <div class="max-w-10 flex justify-center items-center p-2">
            <font-awesome-icon
              :icon="field.icon"
              class="size-[26px] text-gray dark:text-gray-dark"
            ></font-awesome-icon>
          </div>
          <div class="pl-2 w-full">
            <p v-if="field.value" class="mb-0 text-gray dark:text-gray-dark font-medium">
              {{ $t(field.label) }}
            </p>
            <p v-if="field.value && picklists.length" class="text-lg">
              {{
                field.value && getNestedProperty(field.value, field.typeofValue)
                  ? getNestedProperty(field.value, field.typeofValue)
                  : field.defaultValue
                    ? field.defaultValue
                    : '-'
              }}
            </p>
            <p v-else class="mb-0">{{ $t(field.label) }}</p>
          </div>
          <div
            v-if="field.redirectComponent"
            @click="redirect(field)"
            class="w-10 flex justify-center items-center cursor-pointer"
          >
            <font-awesome-icon
              icon="fa-chevron-right"
              class="size-[24px] text-gray dark:text-gray-dark"
            ></font-awesome-icon>
          </div>
        </div>
      </li>
    </WhiteCardComponent>
  </section>
</template>

<script lang="ts">
  import { defineComponent, onMounted } from 'vue';
  export default defineComponent({
    name: 'SectionComponent'
  });
</script>

<script setup lang="ts">
  import { useRoute, useRouter, type RouteLocationNormalizedLoaded } from 'vue-router';
  import { ref, computed, defineAsyncComponent } from 'vue';
  import type { Ref } from 'vue';
  import sections from './utils';
  import companyProfileSectionFields from '@/components/SectionComponent/utils/companyProfileSectionFields';
  import residentialProfileSectionFields from '@/components/SectionComponent/utils/residentialProfileSectionFields';
  import appFields from '@/components/SectionComponent/utils/fields';
  import { useCustomersCare } from '@/stores/customersCare';
  import type {
    ICustomersCareStore,
    ICustomersCareProperty
  } from '@/stores/customersCare/interfaces/store.interface';
  import type { IAppSection, IAppField } from './interfaces/appSectionField.interface';
  import { setAddressString } from 'parlem-webcomponents-common';
  import type { ISectionFieldsValue } from './interfaces/sectionsFieldsValue.interface';
  import { useI18n } from 'vue-i18n';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const route: RouteLocationNormalizedLoaded = useRoute();
  const router = useRouter();
  const sectionFields: Ref<IAppSection[]> = ref([]);
  const customerInfo = computed(() => customersCareStore?.customerInfo);
  const picklists = computed(() => customersCareStore?.picklists);
  const { t } = useI18n();

  onMounted(async () => {
    if (!picklists.value.length) {
      await customersCareStore.getPicklists();
    }
    if (customerInfo.value) {
      getSectionSelected();
    }
  });

  const findAndCloneByKey = <T extends { key: string }>(array: T[], key: string): T | undefined => {
    const found = array?.find((item: T) => item.key === key);
    return found ? { ...found } : undefined;
  };
  const findAndCloneById = <T extends { id: string }>(array: T[], id: string): T | undefined => {
    const found = array.find((item: T) => item.id === id);
    return found ? { ...found } : undefined;
  };

  const getSectionSelected = (): void => {
    const isCompany = customerInfo.value?.personalData?.documentType.toLowerCase() === 'cif';
    const sectionKeys: string[] = isCompany
      ? sections[`${route.name as string}Company`]
      : sections[`${route.name as string}`];

    if (!sectionKeys) return;
    const selectedSections: IAppSection[] = sectionKeys
      .map((sectionKey: string): IAppSection | undefined => {
        //apartats de la pagina
        const sectionFields = isCompany
          ? companyProfileSectionFields
          : residentialProfileSectionFields;
        const foundSection: IAppSection | undefined = findAndCloneByKey(sectionFields, sectionKey);
        if (!foundSection) return undefined;

        // Afegir valor als fields
        if (typeof foundSection.fields[0] === 'string') {
          foundSection.fields = (foundSection.fields as string[])
            .map((fieldId: string): IAppField | undefined => findAndCloneById(appFields, fieldId))
            .filter((field): field is IAppField => field !== undefined);
        } else {
          foundSection.fields = (foundSection.fields as IAppField[]).filter(
            (field: IAppField) => field !== undefined
          );
        }
        return foundSection;
      })

      .filter((section): section is IAppSection => section !== undefined);

    sectionFields.value = selectedSections;
  };

  const getNestedProperty = (
    propertyPath: string,
    typeofValue: string | undefined = undefined
  ): ISectionFieldsValue => {
    const properties: Array<keyof ICustomersCareProperty> = propertyPath.split('.');
    let currentObject: ICustomersCareProperty = customersCareStore;
    for (const property of properties) {
      currentObject = Array.isArray(currentObject?.[property])
        ? currentObject[property].find((object: any) => object.isDefault)
          ? currentObject[property].find((object: any) => object.isDefault)
          : currentObject[property][0]
        : (currentObject?.[property] as ICustomersCareProperty);
    }

    // PENDENT PICKLISTS
    if (propertyPath.includes('nationality')) {
      const countryPicklist = picklists.value.find((picklist: any) => picklist.name === 'Country');
      if (countryPicklist) {
        const match = countryPicklist.values.find((option: any) => option.value === currentObject);
        return match ? match.label : currentObject;
      }
      return currentObject;
    }

    if (propertyPath.includes('preferredLanguage')) {
      const translatedValue = t(`preferredLanguage.${currentObject}`);
      return translatedValue || currentObject;
    }

    return currentObject ? formattedValue(currentObject, typeofValue) : '';
  };

  const formattedValue = (
    value: ISectionFieldsValue,
    typeofValue: string | undefined
  ): ISectionFieldsValue => {
    if (!typeofValue) return value;
    switch (typeofValue) {
      case 'date':
        return new Date(value as string).toLocaleDateString('es-ES');
      case 'address':
        return setAddressString(value as object);
      default:
        return value;
    }
  };
  const redirect = (field: any) => {
    router.push({
      name: field.redirectComponent,
      params: { key: field.key, id: field.id }
    });
  };
</script>
