import { CUSTOMER_INFO } from '../../constants/sectionComponentConstants'
import type { IAppField } from '../../interfaces/appSectionField.interface'

const parentKey = 'shippingAddresses'

const shippingAddressesFields: IAppField[] = [
  {
    key: parentKey,
    id: 'shippingAddresses',
    label: 'profile.shippingAddresses.title-single',
    value: `${CUSTOMER_INFO}.shippingAddresses`,
    typeofValue: 'address',
    icon: 'fa-solid fa-truck-fast',
    redirectComponent: 'ProfileList'
  }
]

export default shippingAddressesFields
