import { CUSTOMER_INFO } from '../../constants/sectionComponentConstants'
import type { IAppField } from '../../interfaces/appSectionField.interface'

const parentKey = 'billingInfos'

const billingInfosFields: IAppField[] = [
  {
    id: 'billingInfoName',
    key: parentKey,
    label: 'profile.billingInfos.name',
    value: `${CUSTOMER_INFO}.billingInfos.name`,
    icon: 'fa-regular fa-circle-user',
    redirectComponent: 'ProfileList'
  },
  {
    id: 'cccOwner',
    key: parentKey,
    label: 'profile.billingInfos.cccOwner',
    value: `${CUSTOMER_INFO}.billingInfos.cccOwner`,
    icon: 'fa-regular fa-circle-user',
    redirectComponent: 'ProfileList'
  },
  {
    id: 'cccOwnerIdentification',
    key: parentKey,
    label: 'profile.billingInfos.cccOwnerIdentification',
    value: `${CUSTOMER_INFO}.billingInfos.cccOwnerIdentification`,
    icon: 'fa-regular fa-id-card',
    redirectComponent: 'ProfileList'
  },
  {
    id: 'iban',
    key: parentKey,
    label: 'profile.billingInfos.iban',
    value: `${CUSTOMER_INFO}.billingInfos.iban`,
    icon: 'fa-regular fa-credit-card',
    redirectComponent: 'ProfileList'
  },
  {
    id: 'sendBill',
    key: parentKey,
    label: 'profile.billingInfos.sendBill',
    value: `${CUSTOMER_INFO}.billingInfos.sendBill`,
    icon: 'fa-regular fa-paper-plane',
    redirectComponent: 'ProfileList'
  }
]

export default billingInfosFields
