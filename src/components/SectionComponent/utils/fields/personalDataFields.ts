import { CUSTOMER_INFO } from '../../constants/sectionComponentConstants'
import type { IAppField } from '../../interfaces/appSectionField.interface'

const parentKey = 'personalData'

const personalDataFields: IAppField[] = [
  {
    id: 'completeName',
    key: parentKey,
    label: 'profile.personalData.completeName',
    value: `${CUSTOMER_INFO}.personalData.completeName`,
    icon: 'fa-regular fa-user'
  },

  {
    id: 'firstName',
    key: parentKey,
    label: 'Nom',
    value: `${CUSTOMER_INFO}.personalData.firstName`,
    icon: 'fa-regular fa-user'
  },
  {
    id: 'lastName',
    key: parentKey,
    label: 'Cognoms',
    value: `${CUSTOMER_INFO}.personalData.lastName`,
    icon: 'fa-regular fa-user'
  },
  {
    id: 'nationality',
    key: parentKey,
    label: 'profile.personalData.nationality',
    value: `${CUSTOMER_INFO}.personalData.nationality`,
    icon: 'fa-regular fa-flag',
    typeofValue: 'country'
  },
  {
    id: 'gender',
    key: parent<PERSON>ey,
    label: '<PERSON><PERSON><PERSON>',
    value: `${CUSTOMER_INFO}.personalData.gender`,
    icon: 'fa-solid fa-venus-mars'
  },
  {
    id: 'documentNumber',
    key: parentKey,
    label: 'profile.personalData.documentNumber',
    value: `${CUSTOMER_INFO}.personalData.documentNumber`,
    icon: 'fa-regular fa-id-card'
  },
  {
    id: 'foundationDate',
    key: parentKey,
    label: 'profile.personalData.foundationDate',
    value: `${CUSTOMER_INFO}.personalData.foundationDate`,
    icon: 'fa-regular fa-star',
    typeofValue: 'date'
  },
  {
    id: 'personBirthdate',
    key: parentKey,
    label: 'profile.personalData.personBirthdate',
    value: `${CUSTOMER_INFO}.personalData.personBirthdate`,
    typeofValue: 'date',
    icon: 'fa-regular fa-star'
  },
  {
    id: 'customerType',
    key: parentKey,
    label: 'Tipus de client',
    value: `${CUSTOMER_INFO}.personalData.customerType`,
    icon: 'fa-regular fa-id-card'
  }
]

export default personalDataFields
