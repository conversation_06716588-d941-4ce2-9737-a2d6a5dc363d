import { CUSTOMER_INFO } from '../../constants/sectionComponentConstants'
import type { IAppField } from '../../interfaces/appSectionField.interface'

const parentKey = 'lastInvoiceHome'

const lastInvoiceHomeFields: IAppField[] = [
  {
    id: 'lastInvoice',
    key: parentKey,
    label: 'home.last-invoice-available',
    // value: `${CUSTOMER_INFO}.billingInfos.sendBill`,
    icon: 'fa-regular fa-file-lines',
    redirectComponent: 'Billing'
  }
]

export default lastInvoiceHomeFields
