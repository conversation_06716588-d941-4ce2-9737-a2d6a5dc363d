import { CUSTOMER_INFO } from '../../constants/sectionComponentConstants'
import type { IAppField } from '../../interfaces/appSectionField.interface'

const parentKey = 'companyStructure'

const companyStructureFields: IAppField[] = [
  {
    id: 'administratorDocumentNumber',
    key: parent<PERSON>ey,
    label: 'profile.companyStructure.administratorDocumentNumber',
    value: `${CUSTOMER_INFO}.companyStructure.administratorDocumentNumber`,
    icon: 'fa-regular fa-id-card'
  },
  {
    id: 'administratorDocumentType',
    key: parentKey,
    label: 'profile.companyStructure.administratorDocumentType',
    value: `${CUSTOMER_INFO}.companyStructure.administratorDocumentType`,
    icon: 'fa-regular fa-id-card'
  },
  {
    id: 'companyManagerFirstName',
    key: parentKey,
    label: 'profile.companyStructure.companyManagerFirstName',
    value: `${CUSTOMER_INFO}.companyStructure.companyManagerFirstName`,
    icon: 'fa-solid fa-user-tie'
  },
  {
    id: 'companyManagerLastName',
    key: parent<PERSON><PERSON>,
    label: 'profile.companyStructure.administratorDocumentType',
    value: `${CUSTOMER_INFO}.companyStructure.companyManagerLastName`,
    icon: 'fa-solid fa-user-tie'
  }
]

export default companyStructureFields
