import type { IAppField } from '../../interfaces/appSectionField.interface'
import { CUSTOMER_INFO } from '../../constants/sectionComponentConstants'

const parentKey = 'billingAddresses'

const billingAddressesFields: IAppField[] = [
  {
    id: 'billingAddresses',
    key: parent<PERSON>ey,
    label: 'profile.billingAddresses.title-single',
    value: `${CUSTOMER_INFO}.billingAddresses`,
    typeofValue: 'address',
    icon: 'fa-regular fa-address-book',
    redirectComponent: 'ProfileList'
  }
]

export default billingAddressesFields
