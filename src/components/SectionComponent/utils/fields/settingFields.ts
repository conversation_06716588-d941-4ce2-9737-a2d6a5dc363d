import { CUSTOMER_INFO } from '../../constants/sectionComponentConstants';
import type { IAppField } from '../../interfaces/appSectionField.interface';
const parentKey = 'settings';

const settingsFields: IAppField[] = [
  {
    id: 'preferredLanguage',
    key: parentKey,
    fieldName: 'preferredLanguage',
    label: 'profile.preferredLanguage',
    value: `${CUSTOMER_INFO}.preferredLanguage`,
    icon: 'fa-solid fa-language',
    redirectComponent: 'ProfileEdit',
    defaultValue: 'ca'
  }
];

export default settingsFields;
