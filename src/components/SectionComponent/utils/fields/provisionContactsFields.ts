import { CUSTOMER_INFO } from '../../constants/sectionComponentConstants'
import type { IAppField } from '../../interfaces/appSectionField.interface'

const parentKey = 'provisionContacts'

const provisionContactsFields: IAppField[] = [
  {
    key: parent<PERSON><PERSON>,
    id: 'name',
    label: 'profile.provisionContacts.name',
    value: `${CUSTOMER_INFO}.provisionContacts.name`,
    icon: 'fa-regular fa-id-badge',
    redirectComponent: 'ProfileList'
  },
  {
    key: parent<PERSON><PERSON>,
    id: 'email',
    label: 'profile.provisionContacts.email',
    value: `${CUSTOMER_INFO}.provisionContacts.email`,
    icon: 'fa-regular fa-envelope',
    redirectComponent: 'ProfileList'
  },
  {
    key: parent<PERSON>ey,
    id: 'phone',
    label: 'profile.provisionContacts.phone',
    value: `${CUSTOMER_INFO}.provisionContacts.phone`,
    icon: 'fa-solid fa-phone',
    redirectComponent: 'ProfileList'
  }
]

export default provisionContactsFields
