import type { IAppSection } from '../interfaces/appSectionField.interface';

const residentialProfileSectionFields: IAppSection[] = [
  {
    title: 'profile.personalData.profile-title',
    key: 'personalData',
    fields: ['completeName', 'nationality', 'personBirthdate']
  },
  {
    title: 'profile.provisionContacts.profile-title',
    key: 'provisionContacts',
    fields: ['name', 'email', 'phone']
  },
  {
    title: 'profile.billingInfos.profile-title',
    key: 'billingData',
    fields: [
      'cccOwner',
      'cccOwnerIdentification',
      'iban',
      'sendBill',
      'billingAddresses',
      'shippingAddresses'
    ]
  },
  {
    title: 'profile.settings.profile-title',
    key: 'settings',
    fields: ['preferredLanguage']
  },
  {
    title: 'management.title',
    key: 'productManagement',
    fields: ['roaming', 'subscriptions']
  },
  {
    title: 'billing.invoice.invoice-config',
    key: 'billingInfos',
    fields: [
      'cccOwner',
      'cccOwnerIdentification',
      'iban',
      'sendBill',
      'billingAddresses',
      'shippingAddresses'
    ]
  }
];

export default residentialProfileSectionFields;
