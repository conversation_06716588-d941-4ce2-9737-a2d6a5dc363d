<template>
  <!-- Always show for debugging -->
  <div v-if="showDebug || offlineReady || needRefresh" class="pwa-toast" role="alert">
    <div class="message">
      <div v-if="showDebug">
        <strong>🔧 PWA Debug Info:</strong><br />
        SW Registered: {{ swRegistered }}<br />
        Offline Ready: {{ offlineReady }}<br />
        Need Refresh: {{ needRefresh }}<br />
      </div>
      <span v-if="offlineReady"> ✅ App ready to work offline </span>
      <span v-else-if="needRefresh">
        🔄 New content available, click on reload button to update.
      </span>
    </div>
    <button v-if="needRefresh" @click="updateServiceWorker()">Reload</button>
    <button @click="close">Close</button>
    <button v-if="showDebug" @click="toggleDebug">Hide Debug</button>
    <button v-else @click="toggleDebug">Show Debug</button>
  </div>
</template>

<script setup lang="ts">
  import { useRegisterSW } from 'virtual:pwa-register/vue';
  import { onMounted, ref } from 'vue';

  const showDebug = ref(true); // Start with debug visible
  const swRegistered = ref(false);

  const { offlineReady, needRefresh, updateServiceWorker } = useRegisterSW({
    onRegisteredSW(swUrl, r) {
      console.log(`Service Worker registered at: ${swUrl}`);
      console.log('Service Worker registration:', r);
      swRegistered.value = true;
    },
    onRegisterError(error) {
      console.error('Service Worker registration error:', error);
      swRegistered.value = false;
    },
    onNeedRefresh() {
      console.log('Service Worker needs refresh');
    },
    onOfflineReady() {
      console.log('Service Worker is offline ready');
    }
  });

  async function close() {
    offlineReady.value = false;
    needRefresh.value = false;
    showDebug.value = false;
  }

  function toggleDebug() {
    showDebug.value = !showDebug.value;
  }

  onMounted(() => {
    console.log('ReloadPrompt component mounted');
    console.log('offlineReady:', offlineReady.value);
    console.log('needRefresh:', needRefresh.value);
  });
</script>

<style>
  .pwa-toast {
    position: fixed;
    right: 0;
    bottom: 0;
    margin: 16px;
    padding: 12px;
    border: 1px solid #8885;
    border-radius: 4px;
    z-index: 1;
    text-align: left;
    box-shadow: 3px 4px 5px 0px #8885;
  }
  .pwa-toast .message {
    margin-bottom: 8px;
  }
  .pwa-toast button {
    border: 1px solid #8885;
    outline: none;
    margin-right: 5px;
    border-radius: 2px;
    padding: 3px 10px;
  }
</style>
