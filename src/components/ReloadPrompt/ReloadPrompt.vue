<template>
  <!-- Always show for debugging -->
  <div class="fixed bottom-0 right-0 m-4">
    <div v-if="showDebug || offlineReady" class="pwa-toast mb-4" role="alert">
      <div class="message">
        <div class="text-sm text-info">
          <p class="font-bold">SERVICE WORKER</p>
          <p class="font-semibold">Debug Info:</p>
          <p class="">
            SW Registered: {{ swRegistered }}<br />
            Offline Ready: {{ offlineReady }}<br />
            Need Refresh: {{ needRefresh }}<br />
          </p>
        </div>
        <span v-if="offlineReady" class="text-sm"> ✅ App ready to work offline </span>
        <div class="flex justify-between text-xs">
          <!-- Debug toggle only available in development or when debug is already shown -->
          <PwButton
            v-if="showDebug"
            text="Hide Debug"
            class="!mb-0 max-h-6"
            :theme="'primary-light'"
            @click="toggleDebug"
          ></PwButton>
          <PwButton
            v-else-if="isDev"
            class="!mb-0 max-h-6"
            text="Show Debug"
            :theme="'primary-light'"
            @click="toggleDebug"
          ></PwButton>
        </div>
      </div>
    </div>
    <div v-if="needRefresh" class="pwa-toast" role="alert">
      <div class="flex flex-col justify-center items-center">
        <font-awesome-icon
          icon="fa-solid fa-arrows-rotate"
          class="size-8 font-bold text-primary my-2"
        ></font-awesome-icon>
        <p class="text-sm text-center font-medium text-info">
          New content available, <br />
          click on reload button to update.
        </p>
      </div>
      <div class="flex justify-between text-xs gap-2">
        <PwButton
          class="!mb-0 max-h-6"
          text="Reload"
          :theme="'primary-light'"
          v-if="needRefresh"
          @click="updateServiceWorker()"
        ></PwButton>
        <PwButton
          class="!mb-0 max-h-6"
          text="Close"
          :theme="'primary-light'"
          @click="close"
        ></PwButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PwButton } from 'parlem-webcomponents-common';
  import { useRegisterSW } from 'virtual:pwa-register/vue';
  import { onMounted, ref } from 'vue';

  const isDev = import.meta.env.DEV;
  const showDebug = ref(isDev); // Only show debug in development
  const swRegistered = ref(false);

  const { offlineReady, needRefresh, updateServiceWorker } = useRegisterSW({
    onRegisteredSW(swUrl, r) {
      console.log(`Service Worker registered at: ${swUrl}`);
      console.log('Service Worker registration:', r);
      swRegistered.value = true;
    },
    onRegisterError(error) {
      console.error('Service Worker registration error:', error);
      swRegistered.value = false;
    },
    onNeedRefresh() {
      console.log('Service Worker needs refresh');
    },
    onOfflineReady() {
      console.log('Service Worker is offline ready');
    }
  });

  async function close() {
    offlineReady.value = false;
    needRefresh.value = false;
    showDebug.value = false;
  }

  function toggleDebug() {
    showDebug.value = !showDebug.value;
  }

  onMounted(() => {
    console.log('ReloadPrompt component mounted');
    console.log('offlineReady:', offlineReady.value);
    console.log('needRefresh:', needRefresh.value);
  });
</script>

<style>
  .pwa-toast {
    padding: 8px;

    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    z-index: 1;
    box-shadow: 3px 4px 5px 0px #8885;
  }
</style>
