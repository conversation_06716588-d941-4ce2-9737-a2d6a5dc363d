/* import { i18n } from '@/i18n'
const t = i18n.t */
import { useAuthStore } from '@/stores/auth/index';
const authStore: any = useAuthStore();
const company: string = authStore.company;
// `${import.meta.env.VITE_LOGOS_URL}/${company.toLowerCase()}`

const tabsNavigation: any[] = [
  {
    name: 'menu.home',
    icon: 'fa-home',
    route: '/home',
    isActive: true
  },
  {
    name: 'menu.billing',
    icon: 'fa-receipt',
    route: '/billing',
    isActive: false
  },
  {
    name: 'menu.products',
    icon: 'fa-boxes',
    route: '/products',
    isActive: false,
    imgActive: `${import.meta.env.VITE_LOGOS_URL}/${company.toLowerCase()}-logo-small.svg`,
    imgInactive: `${import.meta.env.VITE_LOGOS_URL}/${company.toLowerCase()}-logo-small-gray.svg`
  },
  /* {
    name: 'menu.promotions',
    icon: 'fa-gift',
    route: '/promotions',
    isActive: false
  }, */
  {
    name: 'menu.profile',
    icon: 'fa-solid fa-user',
    route: '/profile',
    isActive: false
  }
];

export default tabsNavigation;
