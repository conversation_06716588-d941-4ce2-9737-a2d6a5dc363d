<template>
  <div v-if="index">
    <PwSidebar
      :header="header"
      :tabs="tabs"
      @updateActiveTab="updateActiveTab"
      @tabRoute="routerInfo"
      class="sidebar"
      @pinChanged="handlePinChanged"
    ></PwSidebar>
    <PwBottomNavigationBar
      :tabs="tabs"
      @updateActiveTab="updateActiveTab"
      @tabRoute="routerInfo"
      class="bottom-bar"
    ></PwBottomNavigationBar>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'NavigationMenuComponent'
  };
</script>

<script setup lang="ts">
  import { useRouter, useRoute } from 'vue-router';
  import { ref, watch, onMounted, computed } from 'vue';
  import type { ComputedRef, Ref } from 'vue';
  import { PwSidebar, PwBottomNavigationBar } from 'parlem-webcomponents-common';
  import { useI18n } from 'vue-i18n';
  import type {
    ISidebarTab,
    ISidebarHeader,
    ISidebarRouterObject,
    ISidebarUpdateActiveTabFunction
  } from './interfaces/menu.interface';
  import { useAuthStore } from '@/stores/auth/index';
  import { useCustomersCare } from '@/stores/customersCare';
  import tabsNavigation from './tabsNavigation';

  const emit = defineEmits(['sidebarPinnedChanged']);

  const authStore: any = useAuthStore();
  const customersCareStore = useCustomersCare();
  const router = useRouter();
  const route = useRoute();
  const { t } = useI18n();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const language: ComputedRef<string | undefined> = computed(() => customersCareStore.getLanguage);
  const tabs: Ref<ISidebarTab[]> = ref([]);
  const theme = localStorage.getItem('theme');
  const baseLogoUrl = `${import.meta.env.VITE_LOGOS_URL}/${company.value.toLowerCase()}`;
  const logoLightMode = `${baseLogoUrl}-logo-black.webp`;
  const logoDarkMode = `${baseLogoUrl}-logo-white.webp`;
  const smallLogo = `${baseLogoUrl}-logo-small.svg`;
  const isSidebarPinned: Ref<boolean> = ref(false);
  const index: Ref<number> = ref(1);

  onMounted(() => {
    setTabs();
  });

  const setTabs = (): void => {
    tabs.value = tabsNavigation.map((tab: ISidebarTab) => {
      return { ...tab };
    });
    tabs.value = tabs.value.map((tab: ISidebarTab) => {
      const isBasePath = route.path.startsWith(tab.route);
      tab.name = t(tab.name);
      tab.isActive = isBasePath;
      return tab;
    });
  };

  const header = ref<ISidebarHeader>({
    title: t('app-title'),
    logo: theme === 'dark' ? logoDarkMode : logoLightMode,
    logoSmall: smallLogo,
    pinUrl: `${import.meta.env.VITE_ICONS_URL}/pin-${company.value.toLowerCase()}.svg`,
    pinFillUrl: `${import.meta.env.VITE_ICONS_URL}/pin-filled-${company.value.toLowerCase()}.svg`
  });

  const routerInfo = (routerObject: ISidebarRouterObject) => {
    router.push(routerObject.route);
  };

  const updateActiveTab: ISidebarUpdateActiveTabFunction = (selectedTabName: string) => {
    tabs.value = tabs.value.map((tab: ISidebarTab) => ({
      ...tab,
      isActive: tab.name === selectedTabName
    }));
  };

  const handlePinChanged = (pinned: boolean) => {
    isSidebarPinned.value = pinned;
  };

  watch(isSidebarPinned, (newValue) => {
    emit('sidebarPinnedChanged', newValue);
  });

  watch(language, (newValue) => {
    setTabs();
  });
</script>

<style>
  @media (max-width: 1024px) {
    .sidebar {
      display: none;
    }
    .bottom-bar {
      display: block;
      place-items: center;
    }
  }
  @media (min-width: 1024px) {
    .sidebar {
      display: block;
      place-items: center;
    }
    .bottom-bar {
      display: none;
    }
  }
</style>
