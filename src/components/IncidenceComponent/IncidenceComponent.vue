<!-- IncidenceComponent.vue -->
<template>
  <WhiteCardComponent class="mt-4">
    <p class="font-bold text-lg">{{ $t(`incidence.info`) }} {{ translatedTitle.toLowerCase() }}?</p>

    <PwButton
      :theme="'outline-primary'"
      :text="$t(`incidence.button-incidence-communicate`)"
      @click="goIncidenceView"
    ></PwButton>

    <p>
      {{ $t(`incidence.text-description-incidence-1`) }} <u><a href="tel:1713">1713</a></u>
      {{ $t(`incidence.text-description-incidence-2`) }}
      <u><a href="tel:900373472">900 373 472</a></u>
      {{ $t(`incidence.text-description-incidence-3`) }}
      <u><a href="mailto:<EMAIL>"><EMAIL></a></u>
    </p>
  </WhiteCardComponent>
</template>

<script lang="ts">
import { defineComponent, defineAsyncComponent } from 'vue'
export default defineComponent({
  name: 'IncidenceComponent'
})
</script>

<script setup lang="ts">
import { PwButton } from 'parlem-webcomponents-common'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
const WhiteCardComponent = defineAsyncComponent(
  () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
)

const router = useRouter()
const route = useRoute()
const { t } = useI18n()

const pathSegment = route.path.split('/').pop()?.toLowerCase() || 'default'
const translatedTitle = t(`${pathSegment}.name`)

const goIncidenceView = () => {
  router.push({ path: `${route.path}/incidence` })
}
</script>
