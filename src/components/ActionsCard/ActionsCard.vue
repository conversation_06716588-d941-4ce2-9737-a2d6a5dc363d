<template>
  <div v-if="item" :class="item.isDefault ? 'mb-2' : 'mb-2'">
    <div class="grid lg:grid-cols-11 sm:grid-cols-2 gap-2">
      <div
        v-if="parentKey === 'provisionContacts'"
        class="grid lg:grid-cols-5 sm:grid-cols-5 lg:col-span-8 sm:col-span-2 gap-2"
      >
        <div v-if="item.name" class="flex flex-col col-span-2">
          <p class="">
            {{ $t(`profile.${parentKey}.name`) }}
          </p>
          <p class="mb-2 text-base font-bold">
            {{ item.name }}
          </p>
          <div
            class="border py-0.5 px-2 w-fit cursor-pointer"
            :class="item.isDefault ? 'border-primary' : 'border-gray'"
            @click="item.isDefault ? '' : setByDefault(item)"
          >
            <p v-if="item.isDefault" class="text-primary text-sm">
              {{
                $t(`profile.list.default`, {
                  key: $t(`profile.${parentKey}.title-single`)
                }).toUpperCase()
              }}
            </p>
            <p v-else class="text-gray text-sm">
              {{ $t('profile.list.defaultAction').toUpperCase() }}
            </p>
          </div>
        </div>
        <div v-if="item.email" class="flex flex-col col-span-2">
          <p class="">
            {{ $t(`profile.${parentKey}.email`) }}
          </p>
          <p class="mb-1 text-base font-bold">
            {{ item.email }}
          </p>
        </div>
        <div v-if="item.phone" class="flex flex-col lg:col-span-1">
          <p class="">
            {{ $t(`profile.${parentKey}.phone`) }}
          </p>
          <p class="mb-1 text-base font-bold">
            {{ item.phone }}
          </p>
        </div>
      </div>
      <div
        v-if="parentKey === 'billingInfos'"
        class="grid lg:grid-cols-5 sm:grid-cols-1 lg:col-span-8 sm:col-span-2 gap-2"
      >
        <div v-if="item.cccOwner" class="flex flex-col col-span-2">
          <p class="">
            {{ $t(`profile.${parentKey}.cccOwner`) }}
          </p>
          <p class="text-base font-bold">
            {{ item.cccOwner.toUpperCase() }}
          </p>
          <p class="mb-1 text-base font-bold">
            {{ item.cccOwnerIdentification }}
          </p>
          <div
            class="border py-0.5 px-2 w-fit cursor-pointer"
            :class="item.isDefault ? 'border-primary' : 'border-gray'"
            @click="item.isDefault ? '' : setByDefault(item)"
          >
            <p v-if="item.isDefault" class="text-primary text-sm">
              {{
                $t(`profile.list.default`, {
                  key: $t(`profile.${parentKey}.title-single`)
                }).toUpperCase()
              }}
            </p>
            <p v-else class="text-gray text-sm">
              {{ $t('profile.list.defaultAction').toUpperCase() }}
            </p>
          </div>
        </div>
        <div v-if="item.iban" class="flex flex-col col-span-2">
          <p class="">
            {{ $t(`profile.${parentKey}.iban`) }}
          </p>
          <p class="mb-1 text-base font-bold">
            {{ item.iban }}
          </p>
        </div>
        <div v-if="item.sendBill" class="flex flex-col col-span-1">
          <p class="">
            {{ $t(`profile.${parentKey}.sendBill`) }}
          </p>
          <p class="mb-1 text-base font-bold">
            {{ item.sendBill }}
          </p>
        </div>
      </div>
      <div
        class="lg:col-span-8 sm:col-span-2 gap-2"
        v-else-if="['billingAddresses', 'shippingAddresses'].includes(`${parentKey}`)"
      >
        <div v-if="item" class="flex flex-col">
          <p class="">
            {{ $t(`profile.${parentKey}.title-single`) }}
          </p>
          <p class="mb-1 text-base font-bold">
            {{ setAddress(item) }}
          </p>
          <div
            class="border py-0.5 px-2 w-fit cursor-pointer"
            :class="item.isDefault ? 'border-primary' : 'border-gray'"
            @click="item.isDefault ? '' : setByDefault(item)"
          >
            <p v-if="item.isDefault" class="text-primary text-sm">
              {{
                $t(`profile.list.default`, {
                  key: $t(`profile.${parentKey}.title-single`)
                }).toUpperCase()
              }}
            </p>
            <p v-else class="text-gray text-sm">
              {{ $t('profile.list.defaultAction').toUpperCase() }}
            </p>
          </div>
        </div>
      </div>

      <div class="flex justify-end py-2 lg:col-span-3 sm:col-span-2">
        <PwTabs
          class="flex mt-2 w-full lg:w-min lg:pl-6"
          :tabs="setTabsButton(item)"
          :defaultActiveIndex="'All'"
          @tab-click="handleTabClick"
        />
      </div>
    </div>
    <PopUpComponent
      :title="$t(`profile.${parentKey}.delete`)"
      v-if="popUpItem"
      icon="fa fa-trash"
      @accept="handleAccept"
      @close="closeModal"
    />
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import type {
    IAddress,
    IBillingInfo,
    ICustomerInfo,
    IProvisionContact,
    ICustomerInfoArrayProperties
  } from '@/stores/customersCare/interfaces/customer-info.interface';
  export default defineComponent({
    name: 'ActionsCard'
  });
</script>
<script setup lang="ts">
  import { computed, ref, watch, defineAsyncComponent } from 'vue';
  import type { Ref, PropType, ComputedRef } from 'vue';
  import { useCustomersCare } from '@/stores/customersCare';
  import { PwButton, setAddressString, PwTabs } from 'parlem-webcomponents-common';
  import { useRouter, useRoute } from 'vue-router';
  import { useI18n } from 'vue-i18n';

  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const PopUpComponent = defineAsyncComponent(
    () => import('@/components/PopUpComponent/PopUpComponent.vue')
  );

  const props = defineProps({
    item: {
      type: Object as PropType<ICustomerInfoArrayProperties>,
      required: true
    },
    parentKey: {
      type: String as () => keyof ICustomerInfo,
      required: true
    },
    itemsLenght: {
      type: Number,
      required: true
    }
  });
  const customersCareStore = useCustomersCare();
  const router = useRouter();
  const route = useRoute();
  const showSuccessMessage: Ref<boolean> = ref(false);
  const showErrorMessage: Ref<boolean> = ref(false);
  const popUpItem = computed(() => customersCareStore.popUpItem);
  const { t } = useI18n();

  const tabsButton = {
    edit: {
      label: t('profile.list.editAction'),
      value: 'edit',
      disabled: false,
      active: false,
      icon: 'fa fa-pencil'
    },
    delete: {
      label: t('profile.list.deleteAction'),
      value: 'delete',
      disabled: false,
      active: false,
      icon: 'fa fa-trash'
    }
  };

  const setTabsButton = (item: any) => {
    return item.isDefault ? [tabsButton.edit] : [tabsButton.edit, tabsButton.delete];
  };

  const handleTabClick = (tab: any) => {
    if (tab.value === 'edit') {
      editItem(props.item);
    } else if (tab.value === 'delete') {
      openDeleteModal(props.item);
    }
  };

  const setAddress = (address: any): string => {
    return setAddressString(address as object);
  };

  const customerInfo: ComputedRef<ICustomerInfo | null> = computed(
    () => customersCareStore.customerInfo
  );
  const profileSelectedPropertyInfo: ComputedRef<
    IAddress[] | IProvisionContact[] | IBillingInfo[]
  > = computed(() => {
    const property = customerInfo.value?.[props.parentKey];
    if (Array.isArray(property)) {
      return property as IAddress[] | IProvisionContact[] | IBillingInfo[];
    }
    return [];
  });

  const editItem = (item: ICustomerInfoArrayProperties): void => {
    customersCareStore.setProfileItemToEdit(item);
    router.push({
      name: 'ProfileEdit',
      params: {
        key: props.parentKey,
        id: `${t(`profile.${props.parentKey}.title-single`).split(' ').join('_')}-${item.id}`
      }
    });
  };

  const changeItems = async (
    filterList: IAddress[] | IProvisionContact[] | IBillingInfo[]
  ): Promise<void> => {
    const status: number = await customersCareStore.updateCustomerInfo(props.parentKey, filterList);
    customersCareStore.setProfileItemToEdit(null);
    if ([200, 201].includes(status)) {
      showSuccessMessage.value = true;
    } else {
      showErrorMessage.value = true;
    }
  };

  const openDeleteModal = (item: any) => {
    customersCareStore.setPopUpItem({
      item
    });
  };

  const handleAccept = async () => {
    if (popUpItem.value?.item) {
      await deleteItem(popUpItem.value.item);
      customersCareStore.setPopUpItem(null);
    }
  };

  const closeModal = () => {
    customersCareStore.setPopUpItem(null);
  };

  const deleteItem = async (item: ICustomerInfoArrayProperties): Promise<void> => {
    if (profileSelectedPropertyInfo.value && Array.isArray(profileSelectedPropertyInfo.value)) {
      let filterList: (IAddress | IProvisionContact | IBillingInfo)[] = [];
      filterList = profileSelectedPropertyInfo.value.filter(
        (property: IAddress | IProvisionContact | IBillingInfo) => {
          if ('id' in property) {
            return property.id !== item.id;
          }
        }
      );
      changeItems(filterList as IAddress[] | IProvisionContact[] | IBillingInfo[]);
    }
  };

  const setByDefault = async (item: ICustomerInfoArrayProperties): Promise<void> => {
    if (profileSelectedPropertyInfo.value && Array.isArray(profileSelectedPropertyInfo.value)) {
      let filterList: (IAddress | IProvisionContact | IBillingInfo)[] = [];
      filterList = profileSelectedPropertyInfo.value?.map(
        (property: IAddress | IProvisionContact | IBillingInfo) => {
          if (property) {
            if ('id' in property && 'isDefault' in property) {
              property.isDefault = property.id === item.id;
            }
          }
          return property;
        }
      );
      filterList && changeItems(filterList as IAddress[] | IProvisionContact[] | IBillingInfo[]);
    }
  };

  watch(
    () => props.item,
    () => {}
  );
</script>

<style></style>
