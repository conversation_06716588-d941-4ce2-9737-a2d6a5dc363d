<template>
  <div v-if="mainProduct">
    <ProductHeader />

    <!-- Mòbil i fibra: consums i dades tarifa -->
    <div v-if="['mobile', 'landline'].includes(mainProduct?.provisioningClass.toLowerCase())">
      <ConsumptionSection />
    </div>

    <!-- Fibra -->
    <FiberConsumptionComponent
      v-if="['fiber'].includes(mainProduct?.provisioningClass.toLowerCase())"
      :type-of-product="mainProduct?.provisioningClass.toLowerCase()"
      :selected-product="mainProduct"
    ></FiberConsumptionComponent>

    <StartDateProductComponent
      v-if="!['mobile', 'landline'].includes(mainProduct?.provisioningClass.toLowerCase())"
      class="mt-3"
    ></StartDateProductComponent>

    <!-- Gestions -->
    <ManageProductsComponent
      :provisioningClass="mainProduct.provisioningClass?.toLowerCase() || ''"
      :title="$t('management.title')"
    ></ManageProductsComponent>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'ProductInfo'
  };
</script>

<script setup lang="ts">
  import { computed, defineAsyncComponent, type ComputedRef } from 'vue';
  import { useCustomersCare } from '@/stores/customersCare';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';

  const ManageProductsComponent = defineAsyncComponent(
    () => import('@/components/ManageProductsComponent/ManageProductsComponent.vue')
  );
  const FiberConsumptionComponent = defineAsyncComponent(
    () => import('@/components/FiberConsumptionComponent/FiberConsumptionComponent.vue')
  );
  const ProductHeader = defineAsyncComponent(
    () => import('@/components/ProductHeader/ProductHeader.vue')
  );
  const ConsumptionSection = defineAsyncComponent(
    () => import('@/components/ConsumptionSection/ConsumptionSection.vue')
  );
  const StartDateProductComponent = defineAsyncComponent(
    () => import('@/components/StartDateProductComponent/StartDateProductComponent.vue')
  );
  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const mainProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.getMainProduct
  );
</script>
