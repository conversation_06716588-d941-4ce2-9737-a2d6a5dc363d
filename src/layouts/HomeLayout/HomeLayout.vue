<template>
  <NavigationMenuComponent
    class="z-50 overflow-x-hidden"
    @sidebarPinnedChanged="handleSidebarPinnedChanged"
  ></NavigationMenuComponent>
  <main
    class="flex flex-col justify-center overflow-x-hidden overflow-y-scroll min-h-screen"
    :class="`${sidebarPinned ? 'lg:ml-[256px] lg:w-[calc\(100vw-256px)\]' : 'lg:ml-14 lg:w-[calc\(100vw-56px)\]'}`"
  >
    <div v-if="isLoading" class="flex flex-col items-center">
      <PwLoading
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
        class="mt-8"
      ></PwLoading>
    </div>
    <div v-else class="w-full flex flex-col">
      <header
        ref="header"
        id="header"
        class="flex flex-col justify-end z-10 pb-4"
        :style="backgroundClasses()"
        :class="`${isFixed ? `border-b border-gray-light dark:border-dark-gray h-16  bg-background-color fixed top-0 left-0 right-0 px-4 lg:px-6 ${sidebarPinned ? 'lg:ml-[256px]' : 'lg:ml-14'}` : 'h-50  bg-cover bg-center'}`"
      >
        <div>
          <HeaderComponent v-if="!isFixed"></HeaderComponent>
          <div
            v-else
            class="flex w-full items-end relative"
            :class="themeColor === 'dark' ? 'text-white' : ''"
          >
            <div class="flex w-full justify-center">
              <img :src="logoImage" alt="Parlem Logo" loading="lazy" class="h-8 mt-8" />
            </div>
            <div class="right-4 absolute">
              <router-link :to="{ name: 'Help' }">
                <font-awesome-icon
                  icon="fa-circle-question fa-regular"
                  class="size-[24px] cursor-pointer"
                ></font-awesome-icon>
              </router-link>
              <!-- <router-link :to="{ name: 'Help' }">
                <font-awesome-icon
                  icon="fa-bell fa-regular"
                  class="size-[24px] cursor-pointer"
                ></font-awesome-icon>
              </router-link> -->
            </div>
          </div>
        </div>
      </header>
      <section
        class="h-full w-full min-h-[85vh] max-w-[1040px] lg:m-auto px-4 lg:px-6"
        :class="`${isFixed && !isChildPage ? 'pt-[378px]' : 'pt-[40px]'} ${isChildPage ? 'pt-[72px]' : ''}`"
      >
        <slot></slot>
      </section>
    </div>
  </main>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'HomeLayout'
  });
</script>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { ref, onMounted, onBeforeUnmount, watch, computed, defineAsyncComponent } from 'vue';
  import type { Ref, ComputedRef } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { PwLoading } from 'parlem-webcomponents-common';
  import { useAuthStore } from '@/stores/auth';
  import { updateThemeColor } from '@/utils/colors/updatePwaBackgrounColor';
  import homeImageParlem from '@/assets/images/home-images/home_image_parlem.avif';
  import homeImageAproop from '@/assets/images/home-images/home_image_aproop.avif';
  import homeImageToxo from '@/assets/images/home-images/home_image_toxo.avif';
  import homeImageXartic from '@/assets/images/home-images/home_image_xartic.avif';
  const NavigationMenuComponent = defineAsyncComponent(
    () => import('@/components/NavigationMenuComponent/NavigationMenuComponent.vue')
  );
  const HeaderComponent = defineAsyncComponent(
    () => import('@/components/HeaderComponent/HeaderComponent.vue')
  );

  const authStore: any = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const isLoading: ComputedRef<boolean> = computed(() => authStore.isLoading);
  const route = useRoute();
  const sidebarPinned: Ref<Boolean> = ref(false);
  const routeKeyTitle: Ref<any> = ref('');
  const routeIdTitle: Ref<any> = ref('');
  const isChildPage: ComputedRef<boolean> = computed(() => route.matched.length > 1);
  const themeColor: Ref<string | null> = ref(localStorage.getItem('theme'));

  const logoImage =
    themeColor.value === 'dark'
      ? `${import.meta.env.VITE_LOGOS_URL}/${company.value.toLowerCase()}-logo-white.webp`
      : `${import.meta.env.VITE_LOGOS_URL}/${company.value.toLowerCase()}-logo-black.webp`;
  const backgroundImage: Ref<{ [key: string]: string }> = ref({
    Parlem: homeImageParlem,
    Toxo: homeImageToxo,
    Aproop: homeImageAproop,
    Xartic: homeImageXartic
  });

  const { t } = useI18n();

  onMounted(() => {
    setKeyTitle();
    updatePwaBackgroundColor();
  });

  const handleSidebarPinnedChanged = (isPinned: boolean) => {
    sidebarPinned.value = isPinned;
  };
  const backgroundClasses = () => {
    if (isFixed.value) {
      return {
        backgroundColor: 'var(--color-background)'
      };
    } else {
      return {
        backgroundImage: `url(${backgroundImage.value[company.value]})`
      };
    }
  };

  const setKeyTitle = () => {
    let path = route.path.substring(1);
    path = path.split('/').join('.');
    if (route.params.id) {
      routeIdTitle.value = t(`${path}`);
    }
    if (route.params.key) {
      routeKeyTitle.value = t(`${path}`).toLocaleLowerCase();
    }
  };
  const updatePwaBackgroundColor = () => {
    updateThemeColor('#000000');
  };

  watch(
    () => route,
    (newRoute, oldRoute) => {
      if (newRoute.path !== oldRoute.path) {
        setKeyTitle();
        updatePwaBackgroundColor();
      }
    }
  );
  ////FUNCIÓ SCROLL TITOL------------
  const header: Ref<HTMLElement | null> = ref(null);
  const isFixed = ref(false);
  const headerInitialOffset = ref(0);
  // Función que se ejecuta cuando el usuario hace scroll
  const handleScroll = () => {
    isFixed.value = window.scrollY > headerInitialOffset.value;
  };
  // Añadimos el listener de scroll solo si NO estamos en /home
  onMounted(() => {
    headerInitialOffset.value = 270; //header.value ? header.value.offsetHeight : 0
    window.addEventListener('scroll', handleScroll);
  });

  // Eliminamos el listener de scroll antes de desmontar el componente
  onBeforeUnmount(() => {
    window.removeEventListener('scroll', handleScroll);
  });
  /////-------------------------------
</script>
