<template>
  <NavigationMenuComponent
    class="z-50 overflow-x-hidden"
    @sidebarPinnedChanged="handleSidebarPinnedChanged"
  ></NavigationMenuComponent>

  <main
    class="flex flex-col justify-center dark:text-white overflow-x-hidden overflow-y-scroll min-h-screen px-4 lg:px-6 pt-6 lg:pt-10"
    :class="`${sidebarPinned ? 'lg:ml-[256px] lg:w-[calc\(100vw-256px)\]' : 'lg:ml-14 lg:w-[calc\(100vw-56px)\]'}`"
  >
    <div class="w-full max-w-[1040px] flex flex-col lg:m-auto">
      <header
        ref="header"
        id="header"
        class="flex flex-col justify-end bg-background-color z-10"
        :class="`${
          !isFixed && !isChildPage
            ? 'h-28 pb-5'
            : 'h-16 pb-2 fixed top-0 left-0 right-0 px-4 lg:px-6 '
        } ${isFixed ? 'border-b border-gray-light  dark:border-dark-gray pb-2' : ''} ${isChildPage || isFixed ? (sidebarPinned ? 'lg:ml-[256px]' : 'lg:ml-14') : ''} ${isFixed ? (sidebarPinned ? 'lg:ml-[256px]' : 'lg:ml-14') : ''}`"
      >
        <div class="flex justify-end mb-3" v-if="!isFixed && !isChildPage">
          <router-link :to="{ name: 'Help' }">
            <font-awesome-icon
              icon="fa-circle-question fa-regular"
              class="size-[24px] cursor-pointer"
            ></font-awesome-icon>
          </router-link>
          <!-- <router-link :to="{ name: 'Help' }">
            <font-awesome-icon
              icon="fa-bell fa-regular"
              class="size-[24px] cursor-pointer"
            ></font-awesome-icon>
          </router-link> -->
        </div>
        <div>
          <h1 v-if="!isFixed && !isChildPage" class="text-3xl lg:text-4xl font-bold">
            {{ $t(`${route.meta.title}`) }}
          </h1>
          <div v-else class="flex w-full items-center relative">
            <font-awesome-icon
              v-if="isChildPage"
              @click="goBack"
              icon="fa-chevron-left"
              class="size-[24px] text-gray dark:text-gray-dark cursor-pointer"
            ></font-awesome-icon>
            <h1
              v-if="route.params.id && !showIdInTitle"
              class="font-bold w-full text-lb text-center"
            >
              {{ $t(`${routeIdTitle}`) }}
            </h1>
            <div v-else class="w-full">
              <h1 class="font-bold w-full text-center text-lg">
                {{ $t(`${route.meta.title}`, { key: routeKeyTitle }) }}
              </h1>
              <p
                v-if="isProductPage && mainProduct"
                class="w-full text-sm lg:text-base text-center"
              >
                {{
                  ['mobile', 'landline'].includes(mainProduct.provisioningClass.toLocaleLowerCase())
                    ? mainProduct?.number
                    : mainProduct?.productName
                }}
              </p>
              <p v-else-if="showIdInTitle" class="w-full text-sm lg:text-base text-center">
                {{ route.params.id }}
              </p>
            </div>

            <div v-if="!isChildPage" class="right-0 absolute">
              <router-link :to="{ name: 'Help' }">
                <font-awesome-icon
                  icon="fa-circle-question fa-regular"
                  class="size-[24px] cursor-pointer"
                ></font-awesome-icon>
              </router-link>
              <!-- <router-link :to="{ name: 'Help' } class="ml-6"">
                <font-awesome-icon
                  icon="fa-bell fa-regular"
                  class="size-[24px] cursor-pointer"
                ></font-awesome-icon>
              </router-link> -->
            </div>
          </div>
        </div>
      </header>
      <section
        class="h-full w-full min-h-[85vh]"
        :class="`${isFixed && !isChildPage ? 'pt-[132px]' : ''} ${isChildPage ? '' : ''}`"
      >
        <slot></slot>
      </section>
    </div>
  </main>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'BaseLayout'
  });
</script>

<script setup lang="ts">
  import { useRoute, useRouter } from 'vue-router';
  import { ref, onMounted, onBeforeUnmount, watch, computed, defineAsyncComponent } from 'vue';
  import type { Ref, ComputedRef } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { updateThemeColor } from '@/utils/colors/updatePwaBackgrounColor';
  import { getCssVaribleValue } from '@/utils/colors/getCssVaribleValue';
  import { useCustomersCare } from '@/stores/customersCare';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  const NavigationMenuComponent = defineAsyncComponent(
    () => import('@/components/NavigationMenuComponent/NavigationMenuComponent.vue')
  );

  const route = useRoute();
  const router = useRouter();
  const customersCareStore: any = useCustomersCare();
  const sidebarPinned: Ref<Boolean> = ref(false);
  const routeKeyTitle: Ref<any> = ref('');
  const routeIdTitle: Ref<any> = ref('');
  /*   const selectedProduct: Ref<IPrimaryProduct | null> = ref(null); */
  const language: ComputedRef<string> = computed(() => customersCareStore.getLanguage);
  const isChildPage: ComputedRef<boolean> = computed(() => route.matched.length > 1);
  const isProductPage: ComputedRef<boolean> = computed(() => route.path.includes('/products'));
  const showIdInTitle: ComputedRef<boolean> = computed(() => {
    return route.meta?.showIdInTitle && !['preferredLanguage'].includes(route.params.id?.toString())
      ? true
      : false;
  });
  const mainProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.getMainProduct
  );
  const { t } = useI18n();

  onMounted(() => {
    setKeyTitle();
    updatePwaBackgroundColor();
  });

  const handleSidebarPinnedChanged = (isPinned: boolean) => {
    sidebarPinned.value = isPinned;
  };
  const goBack = () => {
    if (route.path.includes('/profile/edit')) {
      customersCareStore.setProfileItemToEdit(null);
    }
    router.go(-1);
  };

  const setKeyTitle = () => {
    if (!route.path.includes('/products')) {
      customersCareStore.setSelectedProduct(null);
      localStorage.removeItem('selectedService');
    }
    let path = route.path.substring(1);
    path = path.split('/').join('.');
    if (route.params.id) {
      if (showIdInTitle.value) {
        path = path.replace(`.${encodeURIComponent(route.params.id?.toString())}`, '');
        routeKeyTitle.value = t(`${path}`).toLocaleLowerCase();
      } else {
        routeIdTitle.value = t(`${path}`);
      }
    }
    if (route.params.key) {
      routeKeyTitle.value = t(`${path}`).toLocaleLowerCase();
    }
  };

  const updatePwaBackgroundColor = () => {
    updateThemeColor(getCssVaribleValue('--color-background'));
  };

  watch(
    () => route,
    (newRoute, oldRoute) => {
      if (newRoute.path !== oldRoute.path) {
        setKeyTitle();
        updatePwaBackgroundColor();
      }
    }
  );

  watch(language, (newLanguage, oldLanguage) => {
    if (newLanguage !== oldLanguage) {
      setKeyTitle();
    }
  });

  ////FUNCIÓ SCROLL TITOL------------
  const header: Ref<HTMLElement | null> = ref(null);
  const isFixed = ref(false);
  const headerInitialOffset = ref(0);
  // Función que se ejecuta cuando el usuario hace scroll
  const handleScroll = () => {
    isFixed.value = window.scrollY > headerInitialOffset.value;
  };
  // Añadimos el listener de scroll solo si NO estamos en /home
  onMounted(() => {
    headerInitialOffset.value = 50; //header.value ? header.value.offsetHeight : 0
    window.addEventListener('scroll', handleScroll);
  });

  // Eliminamos el listener de scroll antes de desmontar el componente
  onBeforeUnmount(() => {
    window.removeEventListener('scroll', handleScroll);
  });
  /////-------------------------------
</script>
