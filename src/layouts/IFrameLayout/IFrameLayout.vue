<template>
  <NavigationMenuComponent
    class="z-50 overflow-x-hidden"
    @sidebarPinnedChanged="handleSidebarPinnedChanged"
  ></NavigationMenuComponent>
  <main
    class="flex justify-center overflow-hidden min-h-screen"
    :class="`${sidebarPinned ? 'lg:ml-[256px] lg:w-[calc\(100vw-256px)\]' : 'lg:ml-14 lg:w-[calc\(100vw-56px)\]'}`"
  >
    <div class="flex w-full flex-col overflow-hidden">
      <header
        ref="header"
        id="header"
        class="bg-black w-full flex justify-center items-end text-white z-10 h-16 px-4 pb-4 lg:px-6 border-b border-secondary"
        :class="`${sidebarPinned ? 'lg:w-[calc\(100vw-256px)\]' : 'lg:w-[calc\(100vw-56px)\]'}`"
      >
        <font-awesome-icon
          @click="$router.go(-1)"
          icon="fa-chevron-left"
          class="size-[18px] text-gray dark:text-gray-dark cursor-pointer"
        ></font-awesome-icon>
        <h1 v-if="route.params.id" class="text-lg font-bold w-full text-center">
          {{ $t(`${routeIdTitle}`) }}
        </h1>
        <h1 v-else class="text-lg font-bold w-full text-center">
          {{ $t(`${route.meta.title}`, { key: routeKeyTitle }) }}
        </h1>
        <div class="right-4 absolute">
          <router-link :to="{ name: 'Help' }">
            <font-awesome-icon
              icon="fa-circle-question fa-regular"
              class="size-[24px] cursor-pointer"
            ></font-awesome-icon>
          </router-link>
          <!-- <router-link :to="{ name: 'Help' }" class="ml-6">
            <font-awesome-icon
              icon="fa-bell fa-regular"
              class="size-[24px] cursor-pointer"
            ></font-awesome-icon>
          </router-link> -->
        </div>
      </header>
      <section
        class="mb-[80px] lg:mb-0 h-full w-full overflow-hidden"
        :class="`${sidebarPinned ? 'lg:w-[calc\(100vw-256px)\]' : 'lg:w-[calc\(100vw-56px)\]'}`"
      >
        <slot></slot>
      </section>
    </div>
  </main>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'IFrameLayout'
  });
</script>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { ref, onMounted, watch, computed, defineAsyncComponent } from 'vue';
  import type { Ref, ComputedRef } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { updateThemeColor } from '@/utils/colors/updatePwaBackgrounColor';
  const NavigationMenuComponent = defineAsyncComponent(
    () => import('@/components/NavigationMenuComponent/NavigationMenuComponent.vue')
  );

  const route = useRoute();
  const sidebarPinned: Ref<Boolean> = ref(false);
  const routeKeyTitle: Ref<any> = ref('');
  const routeIdTitle: Ref<any> = ref('');
  const isChildPage: ComputedRef<boolean> = computed(() => route.matched.length > 1);
  const { t } = useI18n();

  onMounted(() => {
    setKeyTitle();
    updatePwaBackgroundColor();
  });

  const handleSidebarPinnedChanged = (isPinned: boolean) => {
    sidebarPinned.value = isPinned;
  };

  const setKeyTitle = () => {
    let path = route.path.substring(1);
    path = path.split('/').join('.');
    if (route.params.id) {
      routeIdTitle.value = t(`${path}`);
    }
    if (route.params.key) {
      routeKeyTitle.value = t(`${path}`).toLocaleLowerCase();
    }
  };
  const updatePwaBackgroundColor = () => {
    updateThemeColor('#000000');
  };

  watch(
    () => route,
    (newRoute, oldRoute) => {
      if (newRoute.path !== oldRoute.path) {
        setKeyTitle();
        updatePwaBackgroundColor();
      }
    }
  );
  /////-------------------------------
</script>
