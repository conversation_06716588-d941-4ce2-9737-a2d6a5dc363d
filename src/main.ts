import { createApp, nextTick } from 'vue';
import { create<PERSON>inia } from 'pinia';
import i18nInstance from '@/i18n';
import App from './App.vue';
import router from './router';
import './assets/styles/main.css';
import './assets/styles/initLoading.css';
//import 'parlem-webcomponents-common/dist/parlem-webcomponents-common.css';

// Font awesome
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import VueGtag from 'vue-gtag';

import {
  faLocationDot,
  faChevronUp,
  faWifi,
  faTv,
  faMobileScreenButton,
  faFilm,
  faMinus,
  faChevronDown,
  faFileInvoice,
  faChevronRight,
  faCartPlus,
  faGlobe,
  faPlugCirclePlus,
  faHome,
  faReceipt,
  faBoxes,
  faGift,
  faUser as faUserSolid,
  faUserTie,
  faVenusMars,
  faPhone,
  faLanguage,
  faTruckFast,
  faCircleInfo,
  faChevronLeft,
  faUserLock,
  faQuestion,
  faChartSimple,
  faTowerCell,
  faUserPen,
  faTriangleExclamation,
  faXmark,
  faArrowRightFromBracket,
  faCircleDollarToSlot,
  faHourglassHalf,
  faAnchor,
  faPencil,
  faPen,
  faTrash,
  faCircle,
  faPlus,
  faDownload,
  faArrowLeft,
  faArrowRight,
  faArrowsRotate,
  faTag,
  faMagnifyingGlassMinus,
  faMagnifyingGlassPlus,
  faHandHoldingHand,
  faChartColumn,
  faCalendarDays,
  faCheck,
  faUserCheck,
  faSkullCrossbones,
  faPhoneVolume,
  faList,
  faPaperclip,
  faOtter
} from '@fortawesome/free-solid-svg-icons';
import {
  faIdCard,
  faThumbsDown,
  faStar,
  faUser,
  faFlag,
  faCircleQuestion,
  faCircleXmark,
  faCircleCheck,
  faBell,
  faFileLines,
  faCircleDown,
  faAddressBook,
  faCircleUser,
  faPaperPlane,
  faCreditCard,
  faIdBadge,
  faEnvelope,
  faThumbsUp,
  faFaceSadCry,
  faCommentDots,
  faFaceGrinHearts
} from '@fortawesome/free-regular-svg-icons';

import { faMicrosoft, faGoogle, faFacebook } from '@fortawesome/free-brands-svg-icons';

library.add(
  faOtter,
  faFaceGrinHearts,
  faLocationDot,
  faPlus,
  faDownload,
  faArrowLeft,
  faArrowRight,
  faTag,
  faPen,
  faCircle,
  faSkullCrossbones,
  faChevronUp,
  faChevronDown,
  faFileInvoice,
  faChevronRight,
  faCartPlus,
  faGlobe,
  faPlugCirclePlus,
  faHome,
  faReceipt,
  faBoxes,
  faGift,
  faUserSolid,
  faUserTie,
  faVenusMars,
  faPhone,
  faLanguage,
  faTruckFast,
  faCircleInfo,
  faChevronLeft,
  faUserLock,
  faQuestion,
  faChartSimple,
  faTowerCell,
  faUserPen,
  faTriangleExclamation,
  faXmark,
  faArrowRightFromBracket,
  faCircleDollarToSlot,
  faHourglassHalf,
  faAnchor,
  faIdCard,
  faStar,
  faUser,
  faFlag,
  faCircleQuestion,
  faCircleXmark,
  faCircleCheck,
  faBell,
  faFileLines,
  faCircleDown,
  faAddressBook,
  faCircleUser,
  faPaperPlane,
  faCreditCard,
  faIdBadge,
  faEnvelope,
  faThumbsUp,
  faFaceSadCry,
  faCommentDots,
  faWifi,
  faTv,
  faMobileScreenButton,
  faFilm,
  faMinus,
  faPencil,
  faTrash,
  faMagnifyingGlassMinus,
  faMagnifyingGlassPlus,
  faHandHoldingHand,
  faChartColumn,
  faCalendarDays,
  faCheck,
  faMicrosoft,
  faGoogle,
  faFacebook,
  faUserCheck,
  faThumbsDown,
  faPhoneVolume,
  faList,
  faPaperclip,
  faArrowsRotate
);

const app = createApp(App);
app.use(createPinia());
app.use(router);
app.use(
  VueGtag,
  {
    appName: 'App Parlem',
    pageTrackerScreenviewEnabled: true,
    config: { id: 'G-JKKQ76F9GT' },
    onBeforeSend(event: any) {
      if (navigator.userAgent.toLowerCase().includes('iphone')) {
        event.params.device_type = 'iPhone';
      } else if (navigator.userAgent.toLowerCase().includes('android')) {
        event.params.device_type = 'Android';
      } else {
        event.params.device_type = 'Web';
      }
      return event;
    }
  } as any,
  router
);
app.use(i18nInstance);
app.component('font-awesome-icon', FontAwesomeIcon);
app.mount('#app');

nextTick(() => {
  const loadingScreen = document.getElementById('loading-screen');
  const appElement = document.getElementById('app');

  if (loadingScreen && appElement) {
    loadingScreen.style.display = 'none';
    appElement.style.display = 'block';
  }
});
