import type { AuthenticationResult } from '@azure/msal-browser'
import { initialize, handleRedirect } from '@/utils/msal-b2c/msalFunctions'

export default async function checkUserCredentials(): Promise<AuthenticationResult | void> {
  try {
    await initialize()
    await handleRedirect()
  } catch (error: any) {
    console.error('error a la inicialització', error)
    return await initialize()
  }
}
