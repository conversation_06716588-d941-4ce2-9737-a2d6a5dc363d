import { watch, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useRoute, useRouter } from 'vue-router';
import { logout } from '@/utils/msal-b2c/msalFunctions';

const INACTIVITY_TIME = 10 * 60 * 1000; // 10 minuts

export function useInactivityLogout() {
  const auth = useAuthStore();
  const route = useRoute();
  const router = useRouter();

  let timeoutId: number | null = null;
  const activityEvents = ['mousemove', 'keydown', 'mousedown', 'touchstart'];

  const resetTimer = () => {
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = window.setTimeout(() => {
      logout();
      router.push('/login');
    }, INACTIVITY_TIME);
  };

  const startListening = () => {
    activityEvents.forEach((event) => window.addEventListener(event, resetTimer));
    resetTimer();
  };

  const stopListening = () => {
    activityEvents.forEach((event) => window.removeEventListener(event, resetTimer));
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = null;
  };

  const handleActivityWatcher = () => {
    const requiresAuth =
      route.meta.isAuthRequired === undefined || route.meta.isAuthRequired === true;
    if (auth.isAuthenticated && requiresAuth) {
      startListening();
    } else {
      stopListening();
    }
  };

  // Mira quan canvia la ruta o canvia l'estat d'autenticació
  watch([() => route.fullPath, () => auth.isAuthenticated], handleActivityWatcher, {
    immediate: true
  });

  // Neteja en destruir el component
  onUnmounted(() => {
    stopListening();
  });
}
