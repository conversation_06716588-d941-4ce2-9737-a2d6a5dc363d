import { useAuthStore } from '@/stores/auth/index';
import { PublicClientApplication } from '@azure/msal-browser';
import type { AccountInfo } from '@azure/msal-browser';
import {
  SET_ACCESS_TOKEN,
  SET_ACCOUNT,
  SET_IS_AUTHENTICATED,
  SET_AUTHENTICATION_ERROR
} from '@/stores/auth/constants/store.constants';
import { msalConfig, loginRequest, logoutRequest } from './msalConfig';

const msalInstance: any = new PublicClientApplication(msalConfig);

export const initialize = async () => {
  try {
    await msalInstance.initialize();
  } catch (error) {
    console.error('Initialization error', error);
  }
};

export const login = async () => {
  const authStore: any = useAuthStore();
  try {
    authStore[SET_AUTHENTICATION_ERROR](false);
    // Check if MSAL is initialized before using it
    if (!msalInstance) {
      await msalInstance.initialize();
    }
    await msalInstance.loginRedirect(loginRequest).then((loginResponse: any) => {
      if (loginResponse.idToken) {
        authStore[SET_IS_AUTHENTICATED](true);
        authStore[SET_ACCOUNT](loginResponse.account);
        authStore[SET_ACCESS_TOKEN](loginResponse.idToken);
      }
    });
  } catch (error) {
    authStore[SET_AUTHENTICATION_ERROR](true);
    console.error('Login error:', error);
  }
};

export const logout = async () => {
  const authStore: any = useAuthStore();
  logoutRequest.account = msalInstance.getAccountByHomeId(authStore.accessToken);
  if (!msalInstance) {
    await msalInstance.initialize();
  }
  await msalInstance.logoutRedirect(logoutRequest);
  authStore[SET_IS_AUTHENTICATED](false);
  authStore[SET_ACCOUNT](undefined);
  authStore[`${SET_ACCESS_TOKEN}`](undefined);
  authStore[SET_IS_AUTHENTICATED](false);
  authStore[SET_AUTHENTICATION_ERROR](false);
};

export const handleRedirect = async () => {
  try {
    await msalInstance.handleRedirectPromise();
    const authStore: any = useAuthStore();
    const account: AccountInfo = msalInstance.getAllAccounts()[0];
    const token = authStore.accessToken;
    if (account && account?.idToken && account?.idToken !== token) {
      authStore[SET_ACCOUNT](account);
      authStore[`${SET_ACCESS_TOKEN}`](account?.idToken);
      authStore[SET_IS_AUTHENTICATED](account ? true : false);
      authStore[SET_AUTHENTICATION_ERROR](account?.idToken ? false : true);
      return account;
    }
  } catch (error) {
    console.error('Redirect error:', error);
  }
};
