import b2cPolicies from '@/utils/msal-b2c/policies';
const url = new URL(`${window.location.origin}/login`);

export const msalConfig = {
  auth: {
    clientId: '2c772bd0-6628-4fb1-9edb-d6a4f3eb03f5',
    authority: b2cPolicies.authorities.signUpSignIn.authority,
    knownAuthorities: [b2cPolicies.authorityDomain],
    redirectUri: url.href,
    postLogoutUri: url.href
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: true
  },
  system: {
    allowNativeBroker: false
  }
};

export const loginRequest = {
  scopes: ['offline_access']
};

export const logoutRequest = {
  account: '',
  mainWindowRedirectUri: url.href
};
