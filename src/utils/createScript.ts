async function createScript(env: string, name: string) {
  const existScript: HTMLElement | null = document.getElementById(`wc-${name}`)
  if (!existScript) {
    const script: HTMLScriptElement = document.createElement('script')
    script.setAttribute('src', import.meta.env[env])
    script.setAttribute('id', `wc-${name}`)
    script.async = true
    document.head.appendChild(script)
  }
}

export default createScript
