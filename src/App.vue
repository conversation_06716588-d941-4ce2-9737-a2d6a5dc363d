<template>
  <RouterView v-slot="{ Component }" :key="route.fullPath">
    <component :is="Component" :key="route.path" />
  </RouterView> 
  <!--   <ReloadPrompt />
 -->
</template>

<script setup lang="ts">
  import { computed, onMounted, watch, type ComputedRef, ref, type Ref } from 'vue';
  import { RouterView, useRoute, useRouter } from 'vue-router';
  import { defineUserTheme, changeColors } from 'parlem-webcomponents-common';
  import { useAuthStore } from '@/stores/auth/index';
  import { useCustomersCare } from '@/stores/customersCare';
  import getCompany from './utils/getCompany';
  import { useI18n } from 'vue-i18n';
  import type { IAuthStore } from './stores/auth/interfaces/store.interface';
  import type { ICustomersCareStore } from './stores/customersCare/interfaces/store.interface';
  import ReloadPrompt from '@/components/ReloadPrompt/ReloadPrompt.vue';
  import type { ICustomerInfo } from './stores/customersCare/interfaces/customer-info.interface';
  import { useInactivityLogout } from '@/utils/inactivity-logout/useInactivityLogout';

  const route = useRoute();
  const router = useRouter();
  const authStore: IAuthStore = useAuthStore();
  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const accessToken: ComputedRef<string | undefined> = computed(() => authStore.accessToken);
  const { locale } = useI18n();
  const documentNumber: Ref<string | null> = ref(null);
  const company: ComputedRef<string> = computed(() => authStore.company);
  const customerPreferredLanguage: ComputedRef<string | undefined> = computed(
    () => customersCareStore.getLanguage
  );
  const customerInfo: ComputedRef<ICustomerInfo | null> = computed(
    () => customersCareStore.customerInfo
  );

  onMounted(async () => {
    defineUserTheme();
    const company = getCompany();
    authStore.setCompany(company);
    changeColors(company);
    setLanguage();
    useInactivityLogout();
  });

  const setLanguage = () => {
    const savedLanguage = localStorage.getItem('selectedLanguage');
    if (customerPreferredLanguage.value) {
      locale.value = customerPreferredLanguage.value;
      localStorage.setItem('selectedLanguage', customerPreferredLanguage.value);
    } else if (savedLanguage) {
      locale.value = savedLanguage;
    } else {
      locale.value = 'ca';
      localStorage.setItem('selectedLanguage', locale.value);
    }
  };
  const initAppCalls = async () => {
    try {
      authStore.setIsLoading(true);
      await Promise.all([
        customersCareStore.getCustomerInfo(),
        customersCareStore.getServicesByCustomer(company.value)
      ]);
      setLanguage();

      authStore.setIsLoading(false);
    } catch (error) {
      router.push({ name: 'NotAuthorized' });
      authStore.setIsLoading(false);
    }
  };

  watch(accessToken, async (newAccessToken, oldAccessToken) => {
    if (newAccessToken && newAccessToken !== oldAccessToken && !authStore.authenticationError) {
      await initAppCalls();
    }
  });

  const getInvoice = async () => {
    if (documentNumber.value) {
      await customersCareStore.getInvoicesByNif(documentNumber.value);
      await customersCareStore.getDebtByNif(documentNumber.value);
    }
  };

  watch(
    customerInfo,
    async (newCustomerInfo) => {
      if (newCustomerInfo && newCustomerInfo.personalData?.documentNumber) {
        documentNumber.value = newCustomerInfo.personalData.documentNumber;
        await getInvoice();
      }
    },
    { immediate: true }
  );
</script>
<style>
  @import 'parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
