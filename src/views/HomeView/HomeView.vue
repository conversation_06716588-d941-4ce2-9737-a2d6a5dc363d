<template>
  <div class="pb-24">
    <div v-if="isLoading" class="flex flex-col items-center">
      <PwLoading
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
        class="mt-8"
      ></PwLoading>
      <p class="font-bold mt-4 text-lg text-primary">{{ $t('general.loading') }}...</p>
    </div>
    <div>
      <ContractedProductsComponent></ContractedProductsComponent>
      <p class="font-bold text-xl mb-3 mt-8 dark:text-white">{{ $t('home.management.title') }}</p>
      <div class="grid !grid-cols-2 sm:!grid-cols-3 lg:!grid-cols-4 gap-3">
        <WhiteCardComponent
          v-for="(action, index) in actions"
          :key="index"
          class="flex flex-col items-center col-span-1 justify-center cursor-pointer shadow-sm hover:dark:bg-dark-hover dark:shadow-black hover:dark:shadow-dark-light hover:shadow-primary/50 hover:bg-white/60 hover:font-bold"
          @click="redirect(action.link)"
        >
          <div
            class="bg-background-color dark:bg-dark-gray-light rounded-full size-16 flex items-center mb-2 justify-center"
          >
            <font-awesome-icon :icon="action.icon" class="text-gray dark:text-white size-[36px]" />
          </div>
          <div>
            <p class="text-gray dark:text-gray-dark text-center font-medium">{{ action.label }}</p>
            <p class="text-lg/5 text-center dark:text-white">{{ action.name }}</p>
          </div>
        </WhiteCardComponent>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  export default defineComponent({
    name: 'HomeView'
  });
</script>

<script setup lang="ts">
  import { type ComputedRef, computed, defineAsyncComponent } from 'vue';
  import { useAuthStore } from '@/stores/auth';
  import { PwLoading } from 'parlem-webcomponents-common';
  import { useRouter } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const ContractedProductsComponent = defineAsyncComponent(
    () => import('@/components/ContractedProductsComponent/ContractedProductsComponent.vue')
  );

  const authStore: any = useAuthStore();
  const isLoading: ComputedRef<boolean> = computed(() => authStore.isLoading);
  const company: ComputedRef<string> = computed(() => authStore.company);
  const router = useRouter();
  const { t } = useI18n();

  const actions = [
    {
      icon: 'fa-chart-simple',
      label: t('products.name'),
      name: t('home.management.consumption'),
      link: 'Products'
    },
    {
      icon: 'fa-tower-cell',
      label: t('products.name'),
      name: t('home.management.roaming'),
      link: 'Products'
    },
    {
      icon: 'fa-user-pen',
      label: t('profile.name'),
      name: t('home.management.profile'),
      link: 'Profile'
    },
    {
      icon: 'fa-triangle-exclamation',
      label: t('help.name'),
      name: t('home.management.help'),
      link: 'HomeIncidence'
    }
  ];

  const redirect = (routeName: any) => {
    router.push({ name: routeName });
  };
</script>

<style></style>
