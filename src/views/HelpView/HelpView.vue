<template>
  <WhiteCardComponent>
    <div class="flex flex-col gap-4">
      <div class="flex flex-col gap-2">
        <p class="font-bold text-lg">En què et podem ajudar?</p>
        <p>
          Visita el nostre
          <u
            ><a href="https://parlem.com/contactans/" class="text-primary cursor-pointer"
              >centre d’ajuda</a
            ></u
          >

          on trobaràs algunes de les preguntes més freqüents sobre els temes:
        </p>

        <div class="flex flex-col p-3">
          <p>✔ Procés de contractació</p>
          <p>✔ Configuració telèfon/SIM</p>
          <p>✔ Cobertura de fibra i mòbil de Parlem</p>
          <p>✔ Documentació necessària</p>
          <p>✔ Televisió</p>
          <p>✔ App</p>
          <p>✔ Facturació</p>
          <p>✔ Costos i penalitzacions</p>
          <p>✔ Gestions</p>
        </div>
        <PwButton
          :text="'Visitar el centre d’ajuda de Parlem '"
          :theme="'outline-primary'"
          @click="goTo('HelpCenter')"
        ></PwButton>
      </div>

      <div class="flex flex-col gap-1">
        <p class="font-bold text-lg">No està relacionat amb cap d’aquest temes o no me’n surto</p>

        <p>
          En cas que el teu dubte no estigui relacionat amb cap d'aquestes situacions, posa’t
          en<span
            @click="goTo('Incidence')"
            class="inline-flex text-primary underline underline-offset-2 cursor-pointer"
          >
            contacte</span
          >
          amb nosaltres
        </p>
      </div>
    </div>
  </WhiteCardComponent>
</template>

<script lang="ts">
import { defineComponent, defineAsyncComponent } from 'vue'
export default defineComponent({
  name: 'HelpView'
})
</script>

<script setup lang="ts">
import { PwButton } from 'parlem-webcomponents-common'
import { useRouter } from 'vue-router'
const WhiteCardComponent = defineAsyncComponent(
  () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
)

const router = useRouter()

const goTo = (routerName: string) => {
  router.push({ name: routerName })
}
</script>
