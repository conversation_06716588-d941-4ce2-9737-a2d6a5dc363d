<template>
  <div class="flex flex-col h-screen w-screen p-4 text-center">
    <div class="h-full w-full flex flex-col items-center justify-center">
      <font-awesome-icon
        icon="fa-solid fa-user-lock"
        class="size-[120px] font-bold text-gray"
      ></font-awesome-icon>
      <p class="font-bold text-4xl mb-8 mt-2 dark:text-white">{{ $t('errors.401.title') }}</p>
      <p class="text-lg mb-2 dark:text-white">
        {{ $t('errors.401.message') }}
      </p>
      <p class="text-lg mb-4 dark:text-white">{{ $t('errors.401.submessage') }}</p>
    </div>
    <div class="w-full mb-20 flex flex-col items-center">
      <PwButton
        :text="$t('errors.401.button-message')"
        :theme="themeColor === 'light' ? 'secondary' : 'light'"
        class="px-2 min-w-20 max-h-10 max-w-[300px] lg:max-w-[400px]"
        @click="logoutAction"
      >
      </PwButton>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PwButton } from 'parlem-webcomponents-common';
  import { logout } from '@/utils/msal-b2c/msalFunctions';
  import { ref, type Ref } from 'vue';
  const themeColor: Ref<string | null> = ref(localStorage.getItem('theme'));

  const logoutAction = () => {
    logout();
  };
</script>
