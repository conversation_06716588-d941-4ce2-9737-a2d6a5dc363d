<template>
  <WhiteCardComponent class="mb-4">
    <div v-if="isLoading || isSending" class="flex flex-col items-center">
      <PwLoading
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
        class="mt-8"
      >
      </PwLoading>
      <p v-if="isLoading" class="font-bold mt-4 text-lg text-primary">
        {{ $t('general.loading') }}...
      </p>
    </div>
    <div v-else class="flex flex-col items-center justify-start p-4 py-8">
      <div class="p-2 rounded-full">
        <font-awesome-icon
          :icon="
            roamingService.isActive ? 'fa-regular fa-circle-check' : 'fa-regular fa-circle-xmark'
          "
          class="size-[70px] font-bold"
          :class="roamingService.isActive ? 'text-success' : 'text-error'"
        ></font-awesome-icon>
      </div>

      <p
        class="text-xl font-bold mt-2 mb-3"
        :class="roamingService.isActive ? 'text-success' : 'text-error'"
      >
        {{
          roamingService.isActive
            ? $t('products.roaming.status-roaming-actived')
            : $t('products.roaming.status-roaming-desactived')
        }}
      </p>
    </div>
  </WhiteCardComponent>

  <PwButton
    v-if="!isLoading"
    :text="
      isSending
        ? roamingService.isActive
          ? $t('products.roaming.deactivating')
          : $t('products.roaming.activating')
        : roamingService.isActive
          ? $t('products.roaming.button-roaming-deactivate')
          : $t('products.roaming.button-roaming-activate')
    "
    :theme="'primary-primary-light'"
    :disabled="isSending"
    @click="handleRoamingAction"
  ></PwButton>
  <p v-if="showSuccessMessage" class="text-success font-bold text-center mt-2">
    {{ $t('products.roaming.success-message') }}
  </p>
  <p v-if="showErrorMessage" class="text-error font-bold text-center mt-2">
    {{ $t('products.roaming.error-message') }}
  </p>

  <PopUpComponent
    v-if="popUpItem"
    :title="$t('products.roaming.confirm-deactivate-title')"
    :content="$t('products.roaming.confirm-deactivate-message')"
    :icon="'fa-regular fa-circle-xmark'"
    @accept="handleConfirmation"
    @close="closeModal"
  />

  <!-- ROAMING DESCRIPTION -->
  <div class="mt-8 pb-24">
    <p class="font-bold mt-4 mb-4 text-xl">
      {{ $t('products.roaming.description-roaming-title') }}
    </p>
    <WhiteCardComponent class="!p-3">
      <p>{{ $t('products.roaming.description-roaming-details') }}</p>
      <PwButton class="mt-4" :theme="isDarkMode ? 'outline-black-white' : 'outline-secondary'">
        <router-link :to="{ name: 'RoamingHelpCenter' }">
          {{ $t('products.roaming.description-roaming-price') }}
        </router-link></PwButton
      >
    </WhiteCardComponent>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  export default defineComponent({
    name: 'RoamingView'
  });
</script>

<script setup lang="ts">
  import { onMounted, ref, computed, defineAsyncComponent, type ComputedRef, type Ref } from 'vue';
  import { PwButton, PwLoading } from 'parlem-webcomponents-common';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useAuthStore } from '@/stores/auth/index';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const PopUpComponent = defineAsyncComponent(
    () => import('@/components/PopUpComponent/PopUpComponent.vue')
  );

  const customersCareStore: any = useCustomersCare();
  const authStore: any = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const mainProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.getMainProduct
  );
  const roamingService: any = computed(() => customersCareStore.roamingService || {});
  const isLoading: Ref<boolean> = ref(false);
  const isSending: Ref<boolean> = ref(false);
  const showErrorMessage: Ref<boolean> = ref(false);
  const showSuccessMessage: Ref<boolean> = ref(false);
  const themeMode = localStorage.getItem('theme');
  const isDarkMode: Ref<boolean> = ref(themeMode === 'dark');

  // Variables para el popup de confirmación
  const popUpItem: ComputedRef<any> = computed(() => customersCareStore.popUpItem);

  onMounted(async () => {
    isLoading.value = true;
    if (mainProduct.value) {
      await customersCareStore.getRoamingService(
        company.value,
        mainProduct.value?.contractedSubscriptionId
      );
    }
    isLoading.value = false;
  });

  const handleRoamingAction = () => {
    if (roamingService.value.isActive) {
      customersCareStore.setPopUpItem({
        type: 'roaming-deactivation',
        item: roamingService.value
      });
    } else {
      activeRoamingStatus();
    }
  };

  const closeModal = () => {
    customersCareStore.setPopUpItem(null);
  };

  const handleConfirmation = () => {
    activeRoamingStatus();
    closeModal();
  };

  const activeRoamingStatus = async () => {
    isSending.value = true;
    const customersCareStore: any = useCustomersCare();
    try {
      const response = await customersCareStore.activeRoamingService(
        company.value,
        mainProduct.value?.contractedSubscriptionId,
        !roamingService.value.isActive
      );
      showSuccessMessage.value = response.status === 200;
      showErrorMessage.value = !(response.status === 200);
      return response;
    } catch (error) {
      showErrorMessage.value = true;
    } finally {
      isSending.value = false;
      setTimeout(() => {
        showErrorMessage.value = false;
        showSuccessMessage.value = false;
      }, 2000);
    }
  };
</script>
