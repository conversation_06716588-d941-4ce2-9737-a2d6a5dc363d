<template>
  <div v-if="isLoading" class="flex flex-col items-center w-full">
    <PwLoading
      :company="company"
      loading-style="w-32 h-32"
      loading-image-style="w-[20px]"
      class="mt-8"
    ></PwLoading>
    <p class="font-bold mt-4 text-lg text-primary">{{ $t('general.loading') }} ...</p>
  </div>

  <WhiteCardComponent
    v-else-if="!services || services.length === 0"
    class="flex flex-col items-center !py-[160px]"
  >
    <font-awesome-icon icon="fa-otter" class="size-[140px] text-gray mb-6" />
    <p class="font-bold text-center text-xl text-gray">{{ $t('products.no-available') }}</p>
  </WhiteCardComponent>

  <div v-else class="flex flex-col gap-2 pb-24">
    <StoryCard @productIsLoading="(value) => (productIsLoading = value)"></StoryCard>

    <div v-if="productIsLoading" class="flex flex-col items-center w-full">
      <WhiteCardComponent class="flex flex-col justify-center items-center p-3 w-full">
        <PwLoading
          :company="company"
          loading-style="w-32 h-32"
          loading-image-style="w-[20px]"
          class="mt-8"
        ></PwLoading>
        <p class="font-bold mt-4 mb-8 text-lg text-primary">{{ $t('products.loading') }} ...</p>
      </WhiteCardComponent>
    </div>
    <ProductInfoComponent v-show="!productIsLoading"></ProductInfoComponent>
    <IncidenceComponent></IncidenceComponent>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  import type { IAuthStore } from '@/stores/auth/interfaces/store.interface';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  export default defineComponent({
    name: 'ProductView'
  });
</script>

<script setup lang="ts">
  import { PwLoading } from 'parlem-webcomponents-common';
  import { ref, watch, computed, defineAsyncComponent } from 'vue';
  import type { ComputedRef, Ref } from 'vue';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useAuthStore } from '@/stores/auth/index';

  const StoryCard = defineAsyncComponent(() => import('@/components/StoryCard/StoryCard.vue'));
  const IncidenceComponent = defineAsyncComponent(
    () => import('@/components/IncidenceComponent/IncidenceComponent.vue')
  );
  const ProductInfoComponent = defineAsyncComponent(
    () => import('@/components/ProductInfoComponent/ProductInfoComponent.vue')
  );
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const isLoading: Ref<boolean> = ref(true);
  const productIsLoading: Ref<boolean> = ref(false);
  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const authStore: IAuthStore = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const services: ComputedRef<IPrimaryProduct[] | null> = computed(
    () => customersCareStore.getServices
  );
  watch(
    services,
    (newServices: IPrimaryProduct[] | null) => {
      if (newServices === null || newServices.length > 0) {
        isLoading.value = false;
      }
    },
    { immediate: true }
  );
</script>
