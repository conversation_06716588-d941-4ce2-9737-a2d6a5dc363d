<template>
  <div class="flex flex-col">
    <p class="font-bold">{{ $t(`products.subscription.dataSubscriptions`) }}</p>
    <SubscriptionComponent
      v-for="(item, index) of Array(4)"
      :key="index"
      :subscriptionText="$t(`products.subscription.data3Gb`)"
      :subscriptionPrice="'30,00'"
      class="my-2"
    ></SubscriptionComponent>
  </div>
  <div class="flex flex-col py-2">
    <p class="font-bold">{{ $t(`products.subscription.voiceSubscriptions`) }}</p>
    <SubscriptionComponent
      v-for="(item, index) of Array(2)"
      :key="index"
      :subscriptionText="$t(`products.subscription.voice100Min`)"
      :subscriptionPrice="'8,00'"
      class="my-2"
    ></SubscriptionComponent>
  </div>

  <div class="mb-24">
    <WhiteCardComponent class="flex flex-col gap-3">
      <div class="flex flex-col gap-1">
        <p class="font-bold">{{ $t(`products.subscription.description-subscription-title`) }}</p>
        <p class="leading-none">
          {{ $t(`products.subscription.description-subscription-details`) }}
        </p>
      </div>
      <div class="flex flex-col gap-1">
        <p class="font-bold">{{ $t(`products.subscription.description-subscription-info`) }}</p>
        <p class="leading-none">
          {{ $t(`products.subscription.description-subscription-info-details`) }}
        </p>
      </div>
    </WhiteCardComponent>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';
  export default defineComponent({
    name: 'AddAddonsView'
  });
</script>

<script setup lang="ts">
  import {
    onMounted,
    defineAsyncComponent,
    ref,
    type Ref,
    type ComputedRef,
    computed,
    watch
  } from 'vue';
  import { useRouter } from 'vue-router';
  import { useCustomersCare } from '@/stores/customersCare';

  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const SubscriptionComponent = defineAsyncComponent(
    () => import('@/components/SubscriptionComponent/SubscriptionComponent.vue')
  );

  const router = useRouter();
  const isLoading: Ref<boolean> = ref(false);
  const customersCareStore: any = useCustomersCare();
  const selectedProduct: Ref<IPrimaryProduct | null> = ref(null);
  const savedSelectedProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.selectedProduct
  );
  onMounted(async () => {
    getSelectedProduct();
    await retrieveAddonsRatesByRateCode();
    /*   router.push({ name: 'AddAddonsView' })
     */
  });

  const retrieveAddonsRatesByRateCode = async () => {
    isLoading.value = true;
    try {
      await customersCareStore.retrieveAddonsRatesByRateCode(
        selectedProduct.value?.rateCode,
        selectedProduct.value?.productCode
      );
    } catch (error) {
      console.error('Error retrieving rate:', error);
      /*   rateDetail.value = 'no hi ha informació' */
    } finally {
      isLoading.value = false;
    }
  };

  const getSelectedProduct = () => {
    selectedProduct.value = savedSelectedProduct.value
      ? savedSelectedProduct.value
      : localStorage.getItem('selectedProduct')
        ? JSON.parse(localStorage.getItem('selectedProduct') || '')
        : null;
  };

  watch(savedSelectedProduct, (newProduct) => {
    getSelectedProduct();
    retrieveAddonsRatesByRateCode();
  });
</script>
