<template>
  <div class="mt-12">
    <div v-if="loading" class="flex flex-col items-center">
      <PwLoading
        v-if="loading"
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
        class="mt-8"
      ></PwLoading>
      <p class="font-bold mt-4 text-lg text-primary">{{ $t('general.loading') }}...</p>
    </div>

    <div v-else-if="rateDetail === 404" class="flex justify-center items-center h-32">
      <p class="font-bold text-lg text-gray-600">{{ $t('products.rate.error') }}</p>
    </div>

    <div v-else class="mb-40">
      <div v-if="rateDetail && typeof rateDetail === 'object'">
        <!-- NOM -->
        <div v-if="rateDetail.display?.externalName">
          <p class="font-bold text-2xl mb-5">{{ rateDetail.display.externalName }}</p>
        </div>

        <!-- PRODUCTES -->
        <div v-if="rateDetail.pack" class="mt-4">
          <div class="flex items-center mb-2">
            <p class="font-bold text-xl">{{ $t('products.rate.products') }}</p>
          </div>
          <WhiteCardComponent class="mt-2">
            <ul v-if="rateDetail.pack.products.length">
              <li
                v-for="(product, index) in rateDetail.pack.products"
                :key="index"
                class="flex flex-col"
                :class="
                  index < rateDetail.pack.products.length - 1
                    ? 'border-b border-gray-300 pb-2 mb-2'
                    : ''
                "
              >
                <div class="flex items-center w-full">
                  <div class="p-2 max-w-10 flex justify-center items-center">
                    <font-awesome-icon
                      :icon="getIcon(product)"
                      class="text-gray dark:text-gray-dark size-[25px]"
                    />
                  </div>
                  <div class="pl-2">
                    <p class="font-medium mb-0 text-gray dark:text-gray-dark">
                      {{ $t(`products.type.${product.provisioningClass.toLowerCase()}`) }}
                    </p>
                    <p class="font-bold text-lg mb-0 mb-1">
                      {{ product.display.externalName }}
                    </p>
                    <p
                      v-if="
                        product[`${product.provisioningClass.toLowerCase()}Specification`]
                          ?.mbpsSpeed
                      "
                      class="text-lg -mt-1 font-bold"
                    >
                      <span class="text-base font-normal"
                        >{{
                          product[`${product.provisioningClass.toLowerCase()}Specification`]
                            .mbpsSpeed
                        }}:</span
                      >
                      {{
                        product[`${product.provisioningClass.toLowerCase()}Specification`].mbpsSpeed
                      }}
                      Mbps
                    </p>
                    <p
                      v-if="
                        product[`${product.provisioningClass.toLowerCase()}Specification`]?.mbData
                      "
                      class="text-lg -mt-1 font-bold"
                    >
                      <span class="text-base font-normal"
                        >{{ $t('products.consumption.see-data') }}:</span
                      >
                      {{
                        product[`${product.provisioningClass.toLowerCase()}Specification`].mbData &&
                        product[`${product.provisioningClass.toLowerCase()}Specification`].mbData >=
                          ilimitedData
                          ? $t('products.rate.unlimited')
                          : product[`${product.provisioningClass.toLowerCase()}Specification`]
                              .mbData
                      }}
                      <!--   {{
                        product[`${product.provisioningClass.toLowerCase()}Specification`].mbData
                      }} -->
                    </p>
                    <p
                      v-if="
                        product[`${product.provisioningClass.toLowerCase()}Specification`]?.mbCall
                      "
                      class="text-lg -mt-1 font-bold"
                    >
                      <span class="text-base font-normal"
                        >{{ $t('products.consumption.see-calls') }}:</span
                      >
                      {{
                        product[`${product.provisioningClass.toLowerCase()}Specification`].mbCall
                      }}
                    </p>
                    <p
                      v-if="
                        product[`${product.provisioningClass.toLowerCase()}Specification`]
                          ?.minutesCall
                      "
                      class="text-lg -mt-1 font-bold"
                    >
                      <span class="text-base font-normal"
                        >{{ $t('products.mobile.mobile-to-mobile') }}:</span
                      >
                      {{
                        product[`${product.provisioningClass.toLowerCase()}Specification`]
                          .minutesCall &&
                        product[`${product.provisioningClass.toLowerCase()}Specification`]
                          .minutesCall >= ilimitedCalls
                          ? $t('products.rate.unlimited')
                          : product[`${product.provisioningClass.toLowerCase()}Specification`]
                              .minutesCall + $t('products.minuts')
                      }}
                    </p>
                    <p
                      v-if="
                        product[`${product.provisioningClass.toLowerCase()}Specification`]
                          ?.callFixToFixNational
                      "
                      class="text-lg -mt-1 font-bold"
                    >
                      <span class="text-base font-normal"
                        >{{ $t('products.landline.landline-to-landline') }}:</span
                      >
                      {{
                        product[`${product.provisioningClass.toLowerCase()}Specification`]
                          .callFixToFixNational
                      }}
                      {{ $t('products.minuts') }}
                    </p>
                    <p
                      v-if="
                        product[`${product.provisioningClass.toLowerCase()}Specification`]
                          ?.callFixToMobileNational
                      "
                      class="text-lg -mt-1 font-bold"
                    >
                      <span class="text-base font-normal"
                        >{{ $t('products.landline.landline-to-landline') }}:</span
                      >
                      {{
                        product[`${product.provisioningClass.toLowerCase()}Specification`]
                          .callFixToMobileNational
                      }}
                      {{ $t('products.minuts') }}
                    </p>
                  </div>
                </div>
              </li>
            </ul>
            <p v-else>{{ $t('products.rate.no-products') }}</p>
          </WhiteCardComponent>
        </div>

        <!-- PREUS: NO POSEM PREUS -->

        <!-- CARRECS I PENALITZACIONS -->
        <div v-if="rateDetail.charges?.length" class="mt-4">
          <div class="flex items-center mb-2">
            <p class="font-bold text-xl">{{ $t('products.rate.charges') }}</p>
          </div>
          <WhiteCardComponent class="mt-2">
            <ul>
              <li
                v-for="(charge, index) in rateDetail.charges"
                :key="index"
                class="flex flex-col"
                :class="
                  index < rateDetail.charges.length - 1 ? 'border-b border-gray-300 pb-2 mb-2' : ''
                "
              >
                <div class="w-full flex">
                  <div class="m-w-10 flex justify-center items-center p-2">
                    <font-awesome-icon
                      :icon="
                        charge.chargeType.toLowerCase() === 'penalty'
                          ? 'fa-solid fa-skull-crossbones'
                          : 'fa-solid fa-triangle-exclamation'
                      "
                      class="size-[26px] text-gray dark:text-gray-dark"
                    ></font-awesome-icon>
                  </div>
                  <div class="pl-2">
                    <p class="font-medium mb-0 text-gray dark:text-gray-dark">
                      {{ $t(`products.rate.${charge.chargeType.toLowerCase()}`) }}:
                      {{ charge.chargeSubType }}
                    </p>
                    <p class="text-lg font-bold">
                      <span class="text-base font-normal">{{ $t('products.rate.cost') }}:</span>
                      {{ charge.penaltyPricewithVAT.toFixed(2) }} €
                    </p>
                    <p v-if="charge.notations">
                      {{ charge.notations }}
                    </p>
                  </div>
                </div>
              </li>
            </ul>
          </WhiteCardComponent>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import type { IRate } from '@/stores/customersCare/interfaces/rate.interface';
  export default defineComponent({
    name: 'RateDetailView'
  });
</script>

<script setup lang="ts">
  import {
    onMounted,
    ref,
    watch,
    defineAsyncComponent,
    computed,
    type ComputedRef,
    type Ref
  } from 'vue';
  import { useRoute } from 'vue-router';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useAuthStore } from '@/stores/auth/index';
  import { PwLoading } from 'parlem-webcomponents-common';
  import type { IAuthStore } from '@/stores/auth/interfaces/store.interface';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const route = useRoute();
  const rateCode: Ref<string> = ref('');
  const rateDetail: Ref<IRate | null | number> = ref(null);
  const loading: Ref<boolean> = ref(true);
  const authStore: IAuthStore = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const ilimitedCalls: number = 44640;
  const ilimitedData: number = 500;
  const selectedProductName = ref(localStorage.getItem('selectedProductName') || '');

  onMounted(async () => {
    selectedProductName.value = localStorage.getItem('selectedProductName') || '';
    rateCode.value = Array.isArray(route.params.rateCode)
      ? route.params.rateCode[0]
      : route.params.rateCode || 'Rate code not available';
    await retrieveRateByCode();
  });

  const getIcon = (product: any) => {
    const provisioningClass = product.provisioningClass.toLowerCase();
    if (provisioningClass === 'fiber') {
      return 'wifi';
    } else if (provisioningClass === 'agile') {
      return 'tv';
    } else if (provisioningClass === 'mobile') {
      return 'mobile-screen-button';
    } else if (provisioningClass === 'landline') {
      return 'phone';
    } else if (provisioningClass === 'tv') {
      return 'tv';
    } else if (provisioningClass === 'refund') {
      return 'plus';
    } else {
      return 'minus';
    }
  };

  watch(
    () => customersCareStore.rateDetail,
    (newRateDetail) => {
      if (newRateDetail) {
        rateDetail.value = newRateDetail;
        loading.value = false;
      }
    },
    { immediate: true }
  );

  const retrieveRateByCode = async () => {
    loading.value = true;
    try {
      await customersCareStore.retrieveRateByCode(rateCode.value);
    } catch (error) {
      console.error('Error retrieving rate:', error);
      rateDetail.value = null;
    } finally {
      loading.value = false;
    }
  };
</script>
