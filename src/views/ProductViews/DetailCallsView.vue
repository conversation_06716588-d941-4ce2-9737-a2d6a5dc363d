<template>
  <!-- Estado de carga -->
  <div v-if="loading" class="flex flex-col items-center">
    <PwLoading
      :company="company"
      loading-style="w-32 h-32"
      loading-image-style="w-[20px]"
      class="mt-8"
    ></PwLoading>
    <p class="font-bold mt-4 text-lg text-primary">{{ $t('general.loading') }} ...</p>
  </div>

  <div v-else-if="error" class="text-center text-red-500 py-4">
    <p>{{ $t('products.no-data') }}</p>
  </div>

  <div v-else-if="cdrConsumsByNum.length > 0" class="flex flex-col gap-4 mt-12 mb-24">
    <div
      v-for="(item, index) in cdrConsumsByNum"
      :key="index"
      v-show="+item.trafficUsage > 0 || item.destinationNumber"
      class="flex items-center justify-between bg-white dark:bg-dark-background p-4 rounded-lg shadow"
    >
      <div class="flex items-center gap-2 w-4/12">
        <font-awesome-icon icon="phone-volume" class="text-lg text-success" />
        <span class="text-gray-700 text-sm">{{ item.destinationNumber }}</span>
      </div>

      <div class="w-5/12 text-center">
        <span class="text-gray-600 text-sm">{{ formatDate(item.startDate) }}</span>
      </div>

      <div class="w-3/12 text-right">
        <span class="text-gray-700 text-sm">{{
          formatDuration(item.trafficUsage, item.unit)
        }}</span>
      </div>
    </div>
  </div>

  <!-- Mensaje si no hay datos -->
  <div v-else class="text-center py-4">{{ $t('products.consumption.no-data') }}</div>
</template>

<script setup lang="ts">
  import { computed, onMounted, type ComputedRef, ref } from 'vue';
  import { useCustomersCare } from '@/stores/customersCare';
  import { PwLoading } from 'parlem-webcomponents-common';
  import { useAuthStore } from '@/stores/auth/index';
  import type { IPrimaryProduct } from '@/stores/customersCare/interfaces/products.interface';

  const authStore: any = useAuthStore();

  const loading = ref(true);
  const error = ref(false);
  const company: ComputedRef<string> = computed(() => authStore.company);
  const customersCareStore: any = useCustomersCare();
  const cdrConsumsByNum = computed(() => customersCareStore.cdrConsumsByNum || []);
  const mainProduct: ComputedRef<IPrimaryProduct | null> = computed(
    () => customersCareStore.getMainProduct
  );
  const language: ComputedRef<string | undefined> = computed(() => customersCareStore.getLanguage);

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleString(language.value, options);
  };

  const formatDuration = (usage: string, unit: string) => {
    const durationInSeconds = parseInt(usage, 10);
    if (unit === 'second') {
      const minutes = Math.floor(durationInSeconds / 60);
      const seconds = durationInSeconds % 60;
      return `${minutes} min ${seconds} s`;
    }
    return `${usage} ${unit}`;
  };

  // Cargar datos
  onMounted(async () => {
    try {
      loading.value = true;
      await customersCareStore.getRetrieveSubscriptionCDRConsumsByNumber(
        company.value,
        mainProduct.value?.number
      );
      error.value = false;
    } catch (err) {
      console.error('Error loading data:', err);
      error.value = true;
    } finally {
      loading.value = false;
    }
  });
</script>
