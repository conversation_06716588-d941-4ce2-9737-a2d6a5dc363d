<template>
  <div v-if="isLoading" class="flex flex-col items-center">
    <PwLoading
      :company="company"
      loading-style="w-32 h-32"
      loading-image-style="w-[20px]"
      class="mt-8"
    ></PwLoading>
    <p class="font-bold mt-4 text-lg text-primary">
      {{ $t('general.loading') }} {{ $t('billing.name').toLowerCase() }}...
    </p>
  </div>
  <div v-else class="flex flex-col pb-24">
    <div v-if="invoices.length > 0">
      <!-- Grafiques factures -->
      <ChartBillComponent></ChartBillComponent>

      <!-- Historial Factures -->
      <InvoiceHistoryComponent></InvoiceHistoryComponent>
    </div>
    <WhiteCardComponent v-else class="flex flex-col items-center !py-10">
      <font-awesome-icon icon="fa-paperclip" class="size-[140px] text-gray mb-6" />
      <p class="font-bold text-center text-xl text-gray">
        {{ $t('billing.no-invoice-available') }}
      </p>
    </WhiteCardComponent>

    <SectionComponent v-if="hasCustomerInfo"></SectionComponent>

    <!-- Incidence Component -->
    <IncidenceComponent class="mt-6"></IncidenceComponent>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'BillingView'
  });
</script>

<script setup lang="ts">
  import { computed, type ComputedRef, defineAsyncComponent } from 'vue';
  import { useCustomersCare } from '@/stores/customersCare';
  import { PwLoading } from 'parlem-webcomponents-common';
  import { useAuthStore } from '@/stores/auth';
  import type { IAuthStore } from '@/stores/auth/interfaces/store.interface';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  import type { IInvoice } from '@/stores/customersCare/interfaces/invoice.interface';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const ChartBillComponent = defineAsyncComponent(
    () => import('@/components/ChartBillComponent/ChartBillComponent.vue')
  );
  const IncidenceComponent = defineAsyncComponent(
    () => import('@/components/IncidenceComponent/IncidenceComponent.vue')
  );
  const InvoiceHistoryComponent = defineAsyncComponent(
    () => import('@/components/InvoiceHistoryComponent/InvoiceHistoryComponent.vue')
  );
  const SectionComponent = defineAsyncComponent(
    () => import('@/components/SectionComponent/SectionComponent.vue')
  );

  const authStore: IAuthStore = useAuthStore();
  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const hasCustomerInfo: ComputedRef<boolean> = computed(() => !!customersCareStore.customerInfo);
  const isLoading: ComputedRef<boolean> = computed(() => authStore.isLoading);
  const company: ComputedRef<string> = computed(() => authStore.company);
  const invoices: ComputedRef<IInvoice[]> = computed(() => {
    let invoices: IInvoice[] = customersCareStore.invoicesByNif?.invoices || [];
    if (invoices.length > 0) {
      invoices = invoices.sort(
        (invoiceA: IInvoice, invoiceB: IInvoice) =>
          new Date(invoiceB.fecEmision).getTime() - new Date(invoiceA.fecEmision).getTime()
      );
    }
    return invoices;
  });
</script>

<style></style>
