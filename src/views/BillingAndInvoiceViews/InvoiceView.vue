<template>
  <!--   <p class="mb-4">
    {{ $t('billing.invoice.invoice-for') }}
    {{ selectedPeriodDisplay }}
  </p> -->
  <div
    v-if="pdfUrl"
    class="bg-black/80 text-white fixed rounded-full right-2 lg:right-4 bottom-24 lg:bottom-6 size-11 lg:size-14 z-10 flex justify-center items-center"
    @click="getInvoiceByInvoiceNumberForDownload"
  >
    <font-awesome-icon :icon="'download'" class="size-5 lg:size-6" :class="''" />
  </div>

  <div class="h-[calc(100vh-185px)] lg:h-[calc(100vh-96px)] lg:-mt-12 -mt-3">
    <div v-if="loading" class="flex flex-col items-center">
      <PwLoading
        v-if="loading"
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
        class="mt-8"
      ></PwLoading>
      <p class="font-bold mt-4 text-lg text-primary">
        {{ $t('general.loading') }} {{ $t('billing.name-single').toLowerCase() }}...
      </p>
    </div>
    <PdfViewer v-else-if="pdfUrl" :pdfUrl="pdfUrl" class="h-full" />
    <p v-else-if="pdfError" class="text-center text-lg pt-20 text-red-600">
      {{ $t('billing.invoice.no-generate-invoice') }}
    </p>
    <p v-else class="text-center text-lg pt-20">{{ $t('billing.invoice.no-invoice') }}</p>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface'
export default defineComponent({
  name: 'InvoiceView'
})
</script>

<script setup lang="ts">
import { computed, onMounted, ref, type ComputedRef, type Ref, defineAsyncComponent } from 'vue'
import { useCustomersCare } from '@/stores/customersCare'
import { useRoute } from 'vue-router'
import { PwLoading } from 'parlem-webcomponents-common'
import { useAuthStore } from '@/stores/auth'
const PdfViewer = defineAsyncComponent(() => import('@/components/PdfViewer/PdfViewer.vue'))

const customersCareStore: ICustomersCareStore = useCustomersCare()
const authStore: any = useAuthStore()
const route = useRoute()
const company: ComputedRef<string> = computed(() => authStore.company)
const pdfUrl: Ref<string> = ref('')
const invoiceNumber: Ref<string> = ref(route.params.id as string)
const loading: Ref<boolean> = ref(true)
const pdfError: Ref<boolean> = ref(false)

onMounted(async () => {
  await getInvoicePdf()
})

const selectedPeriod: Ref<string> = ref(localStorage.getItem('selectedInvoicePeriod') || '')
const selectedPeriodDisplay = computed(() => `${selectedPeriod.value}`)

const base64ToUint8Array = (base64: any) => {
  const binaryString = window.atob(base64)
  const len = binaryString.length
  const bytes = new Uint8Array(len)
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  return bytes
}

const isValidPdf = (base64: string): boolean => {
  try {
    const decodedString = window.atob(base64)
    return decodedString.startsWith('%PDF-')
  } catch (error) {
    console.error('Error al verificar el contenido del PDF:', error)
    return false
  }
}

const getInvoicePdf = async () => {
  try {
    const pdfValue = await customersCareStore.getInvoiceByInvoiceNumber(invoiceNumber.value)

    if (pdfValue?.length && isValidPdf(pdfValue)) {
      const uint8Array = base64ToUint8Array(pdfValue)
      const pdfBlob = new Blob([uint8Array], { type: 'application/pdf' })
      pdfUrl.value = URL.createObjectURL(pdfBlob)
      pdfError.value = false
    } else {
      pdfError.value = true
    }
  } catch (error) {
    console.error('Error:', error)
    pdfError.value = true
  } finally {
    loading.value = false
  }
}

const getInvoiceByInvoiceNumberForDownload = async () => {
  const link = document.createElement('a')
  link.href = pdfUrl.value
  link.download = `Factura_${selectedPeriodDisplay.value}_${invoiceNumber.value}.pdf`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>
