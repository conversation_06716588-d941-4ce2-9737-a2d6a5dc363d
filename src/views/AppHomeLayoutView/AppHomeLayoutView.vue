<template>
  <HomeLayout v-if="route.fullPath">
    <component :is="routes[currentComponent]"></component>
  </HomeLayout>
</template>

<script setup lang="ts">
  import HomeLayout from '@/layouts/HomeLayout/HomeLayout.vue';
  import { useRoute } from 'vue-router';
  import baseLayoutViews from '@/views/baseLayoutViews';
  import { watch, ref } from 'vue';
  import type { Ref } from 'vue';

  const route = useRoute();
  const currentComponent: Ref<string> = ref('HomeView');
  const routes: any = baseLayoutViews;

  watch(
    () => route,
    (newRoute, oldRoute) => {
      if (newRoute.path !== oldRoute?.path) {
        currentComponent.value = newRoute.meta.component as string;
      }
    },
    { immediate: true, deep: true }
  );
</script>
