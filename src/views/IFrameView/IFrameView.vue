<template>
  <iframe :title="route.fullPath" :src="urlLink" class="block w-full h-full !overflow-hidden">
  </iframe>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  export default defineComponent({
    name: 'IFrameView'
  });
</script>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import type { Ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { useAuthStore } from '@/stores/auth/index';

  const authStore = useAuthStore();
  const route = useRoute();
  const urlLink: Ref<string> = ref('');
  const company: string = authStore.company;

  onMounted(() => {
    const metaUrl = route.meta.url as { [key: string]: string };
    urlLink.value = metaUrl[company];
  });
</script>
