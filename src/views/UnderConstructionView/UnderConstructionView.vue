<template>
  <div class="w-full h-full flex flex-col justify-center items-center pb-6">
    <div
      class="flex flex-col justify-center items-center mt-6 h-[350px] w-[350px] lg:w-[500px] lg:max-w-[500px] lg:h-[500px] lg:max-h-[500px] bg-white dark:bg-dark-background-gray-light rounded-full"
    >
      <img
        :src="underConstructionImage"
        alt="Under Construction"
        loading="lazy"
        height="350"
        width="500"
        class="max-w-[1040] min-w-40 h-[250px] lg:h-[350px] lg:max-h-[350px]"
      />
    </div>
    <h1 class="font-bold xs:text-2xl lg:text-4xl text-center mt-4">
      {{ $t('under-construction.title') }}
    </h1>
    <p class="text-center pt-2 xs:text-base lg:text-lg">
      {{ $t('under-construction.message') }}
    </p>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  export default defineComponent({
    name: 'UnderConstructionView'
  });
</script>
<script setup lang="ts">
  const underConstructionImage = `${import.meta.env.VITE_VIEWS_URL}/under-construction.svg`;
</script>
