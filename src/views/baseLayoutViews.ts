import ProfileView from '@/views/ProfileViews/ProfileView.vue';
import ProfileEditView from '@/views/ProfileViews/ProfileEditView.vue';
import ProfileListView from '@/views/ProfileViews/ProfileListView.vue';
import BillingView from '@/views/BillingAndInvoiceViews/BillingView.vue';
import ProductView from '@/views/ProductViews/ProductView.vue';
import IFrameView from '@/views/IFrameView/IFrameView.vue';
import RoamingView from '@/views/ProductViews/RoamingView.vue';
import AddAddonsView from '@/views/ProductViews/AddAddonsView.vue';
import RateDetailView from '@/views/ProductViews/RateDetailView.vue';
import HomeView from './HomeView/HomeView.vue';
import HelpView from './HelpView/HelpView.vue';
import IncidenceView from './IncidenceView/IncidenceView.vue';
import InvoiceView from '@/views/BillingAndInvoiceViews/InvoiceView.vue';
import DetailCallsView from '@/views/ProductViews/DetailCallsView.vue';
import UnderConstructionView from '@/views/UnderConstructionView/UnderConstructionView.vue';
import SignUpView from '@/views/LoginAndSignUpViews/SignUpView.vue';

const baseLayoutViews = {
  ProfileView,
  ProfileEditView,
  ProfileListView,
  BillingView,
  ProductView,
  AddAddonsView,
  RoamingView,
  RateDetailView,
  IFrameView,
  HomeView,
  HelpView,
  IncidenceView,
  InvoiceView,
  DetailCallsView,
  UnderConstructionView,
  SignUpView
};

export default baseLayoutViews;
