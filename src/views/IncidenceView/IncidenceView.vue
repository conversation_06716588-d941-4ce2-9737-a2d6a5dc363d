<template>
  <WhiteCardComponent class="mb-36 mt-16">
    <PwWebToCase></PwWebToCase>
  </WhiteCardComponent>
</template>

<script lang="ts">
  import { defineComponent, defineAsyncComponent } from 'vue';
  export default defineComponent({ name: 'IncidenceView' });
</script>

<script setup lang="ts">
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const PwWebToCase = defineAsyncComponent(
    () => import('@/components/PwWebToCase/PwWebToCase.vue')
  );
</script>
