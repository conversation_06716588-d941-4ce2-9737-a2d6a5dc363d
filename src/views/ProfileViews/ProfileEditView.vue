<template>
  <div v-if="loading || isSending" class="flex flex-col items-center">
    <PwLoading
      :company="company"
      loading-style="w-32 h-32"
      loading-image-style="w-[20px]"
      class="mt-8"
    ></PwLoading>
    <p class="font-bold mt-4 text-lg text-primary">
      {{ isSending ? $t('profile.edit.sending-data') : $t('profile.edit.loading-data') }}
    </p>
  </div>
  <WhiteCardComponent
    v-else
    class="mb-24"
    :class="
      ['billingAddresses', 'shippingAddresses'].includes(`${parentKey}`) ? 'mt-14 md:mt-0' : ''
    "
  >
    <div
      v-if="showSuccessMessage && key"
      class="flex flex-col items-center justify-center py-20 px-4"
    >
      <div class="p-8 rounded-full bg-primary-light">
        <font-awesome-icon
          icon="fa-regular fa-thumbs-up"
          class="size-[70px] text-primary font-bold"
        ></font-awesome-icon>
      </div>
      <p class="font-bold text-lg mt-3 text-primary text-center">
        {{
          $route.path.includes('edit') ? $t('profile.edit.success') : $t('profile.create.success')
        }}
      </p>
      <router-link :to="{ name: 'Profile' }" class="w-full">
        <PwButton
          :text="$t('profile.edit.back-button')"
          theme="primary-primary-light"
          class="px-2 mt-4 max-h-8"
        >
        </PwButton>
      </router-link>
    </div>
    <div v-else-if="showErrorMessage" class="flex flex-col items-center justify-center py-20 px-4">
      <div class="p-8 rounded-full bg-error-light">
        <font-awesome-icon
          icon="fa-solid fa-xmark"
          class="size-[70px] text-error font-bold"
        ></font-awesome-icon>
      </div>
      <p class="font-bold text-lg mt-3 text-error text-center">
        {{ $t('profile.edit.error') }}
      </p>
      <router-link :to="{ name: 'Profile' }" class="w-full">
        <PwButton
          :text="$t('profile.edit.back-button')"
          theme="secondary"
          class="px-2 min-w-20 mt-4 max-h-8"
        >
        </PwButton>
      </router-link>
    </div>
    <div v-else>
      <div
        v-if="['billingAddresses', 'shippingAddresses'].includes(`${parentKey}`)"
        class="p-4 px-6"
      >
        <div v-if="isEditingRoute">
          <p class="font-bold">{{ $t('profile.billingAddresses.actual-address') }}</p>
          <p class="mb-4 mt-1">{{ setAddress(itemToEdit) }}</p>
        </div>
        <p class="font-bold">{{ $t('profile.billingAddresses.new-address') }}</p>
        <PwStreetMap
          :address="itemToEdit"
          @saveAddress="submitForm"
          from="profileEditView"
        ></PwStreetMap>
      </div>

      <FormComponent v-else :itemToEdit="itemToEdit" @submit="submitForm"></FormComponent>
    </div>
  </WhiteCardComponent>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'ProfileEditView'
  });
</script>
<script setup lang="ts">
  import { computed, ref, watch, defineAsyncComponent } from 'vue';
  import type { ComputedRef, Ref } from 'vue';
  import { setAddressString, PwButton, PwLoading } from 'parlem-webcomponents-common';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useAuthStore } from '@/stores/auth';
  import { useRoute } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import type {
    ICustomerInfo,
    ICustomerInfoArrayProperties
  } from '@/stores/customersCare/interfaces/customer-info.interface';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const PwStreetMap = defineAsyncComponent(
    () => import('@/components/PwStreetMap/PwStreetMap.vue')
  );
  const FormComponent = defineAsyncComponent(
    () => import('@/components/FormComponent/FormComponent.vue')
  );

  const route = useRoute();
  const { locale }: any = useI18n();
  const key: Ref<number> = ref(1);
  const customersCareStore = useCustomersCare();
  const authStore: any = useAuthStore();
  const parentKey: Ref<keyof ICustomerInfo> = ref(route.params.key as keyof ICustomerInfo);
  const showSuccessMessage: Ref<any> = ref(false);
  const showErrorMessage: Ref<any> = ref(false);
  const customerInfo: ComputedRef<ICustomerInfo | null> = computed(
    () => customersCareStore.customerInfo
  );
  const profileSelectedPropertyInfo: ComputedRef<
    ICustomerInfo[keyof ICustomerInfo][] | ICustomerInfo[keyof ICustomerInfo]
  > = computed(() => customerInfo.value?.[parentKey.value] || []);

  const itemToEdit: ComputedRef<any> = computed(() => customersCareStore.profileItemToEdit);
  const loading = ref(false);
  const isSending = ref(false);
  const isEditingRoute = ref(route.path.includes('edit'));
  const company: ComputedRef<string> = computed(() => authStore.company);

  const listItemsOrdered: ComputedRef<ICustomerInfoArrayProperties[]> = computed(() => {
    if (Array.isArray(profileSelectedPropertyInfo.value)) {
      let items = profileSelectedPropertyInfo.value.map((listItem: any, index: number) => {
        return { ...listItem, id: index + 1 };
      });
      return items.sort(
        (firstListItem: any, secondListItem: any) =>
          Number(secondListItem.isDefault) - Number(firstListItem.isDefault)
      );
    }
    return [];
  });

  const setAddress = (address: any): string => {
    if (!address) return '-';
    return setAddressString(address as object);
  };

  const submitForm = async (itemToSubmit: any) => {
    showSuccessMessage.value = false;
    showErrorMessage.value = false;
    authStore.setIsLoading(true);
    isSending.value = true;
    let status: number = 1;
    if (route.params.id === 'preferredLanguage') {
      locale.value = itemToSubmit;
      localStorage.setItem('selectedLanguage', itemToSubmit);
      status = await customersCareStore.updateCustomerInfo('preferredLanguage', itemToSubmit);
    } else if (Array.isArray(profileSelectedPropertyInfo.value)) {
      if (route.path.includes('edit')) {
        const index = profileSelectedPropertyInfo.value?.findIndex(
          (info: any) => info.id === itemToSubmit.id
        );
        if (index === -1) {
          customersCareStore.setProfileItemToEdit(null);
          showErrorMessage.value = true;
          authStore.setIsLoading(false);
          isSending.value = false;
          return;
        }
        profileSelectedPropertyInfo.value.splice(index, 1, itemToSubmit);
      } else {
        profileSelectedPropertyInfo.value.push(itemToSubmit);
      }
      status = await customersCareStore.updateCustomerInfo(
        parentKey.value,
        profileSelectedPropertyInfo.value
      );
    }
    customersCareStore.setProfileItemToEdit(null);
    if (status) {
      if ([200, 201].includes(status)) {
        showSuccessMessage.value = true;
      } else {
        showErrorMessage.value = true;
      }
    }

    authStore.setIsLoading(false);
    isSending.value = false;
  };

  watch(
    () => customerInfo,
    () => {
      const paramsId = route.params.id.toString().split('-');
      const routeId = paramsId[paramsId.length - 1];
      const item = listItemsOrdered.value.find((item: any) => item.id === +routeId);
      customersCareStore.setProfileItemToEdit(item);
      loading.value = false;
    },
    { deep: true }
  );

  watch(
    customerInfo,
    (newValue) => {
      if (!newValue) {
        loading.value = true;
      } else {
        loading.value = false;
      }
    },
    { immediate: true }
  );
</script>

<style></style>
