<template>
  <div v-if="isLoading" class="flex flex-col items-center">
    <PwLoading
      :company="company"
      loading-style="w-32 h-32"
      loading-image-style="w-[20px]"
      class="mt-8"
    ></PwLoading>
  </div>
  <div v-else class="pb-24">
    <WhiteCardComponent v-if="customerInfo && customerInfo.personalData">
      <div class="flex items-center">
        <div
          class="size-[48px] bg-gray-light dark:bg-dark-gray-light rounded-full flex justify-center items-center"
        >
          <font-awesome-icon
            icon="fa-solid fa-user"
            class="size-[30px] text-white"
          ></font-awesome-icon>
        </div>
        <div class="pl-2.5 ml-2">
          <p class="text-xl font-bold">{{ customerInfo.personalData.completeName }}</p>
          <p class="text-lg">
            {{ customerInfo.personalData.documentType.toUpperCase() }}:
            {{ customerInfo.personalData.documentNumber }}
          </p>
        </div>
      </div>
    </WhiteCardComponent>
    <SectionComponent></SectionComponent>
    <WhiteCardComponent class="mt-2" @click="openDeleteModal()">
      <div class="flex items-center cursor-pointer">
        <div class="max-w-10 flex justify-center items-center p-2">
          <font-awesome-icon
            icon="fa-arrow-right-from-bracket"
            class="size-[26px] text-gray dark:text-gray-dark"
          ></font-awesome-icon>
        </div>
        <div class="pl-2">
          <p class="text-lg">{{ $t('profile.logout') }}</p>
        </div>
      </div>
    </WhiteCardComponent>
    <IncidenceComponent></IncidenceComponent>
    <PopUpComponent
      :title="$t('profile.logout-confirm')"
      icon="fa-regular fa-face-sad-cry"
      v-if="popUpItem"
      @accept="handleAccept"
      @close="closeModal"
    />
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  export default defineComponent({
    name: 'ProfileView'
  });
</script>
<script setup lang="ts">
  import { computed, defineAsyncComponent } from 'vue';
  import type { ComputedRef } from 'vue';
  import { useCustomersCare } from '@/stores/customersCare';
  import { PwLoading } from 'parlem-webcomponents-common';
  import { useAuthStore } from '@/stores/auth';
  import { logout } from '@/utils/msal-b2c/msalFunctions';
  import { useRouter } from 'vue-router';
  import type { IAuthStore } from '@/stores/auth/interfaces/store.interface';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );
  const PopUpComponent = defineAsyncComponent(
    () => import('@/components/PopUpComponent/PopUpComponent.vue')
  );
  const SectionComponent = defineAsyncComponent(
    () => import('@/components/SectionComponent/SectionComponent.vue')
  );
  const IncidenceComponent = defineAsyncComponent(
    () => import('@/components/IncidenceComponent/IncidenceComponent.vue')
  );

  const authStore: IAuthStore = useAuthStore();
  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const router = useRouter();
  const popUpItem = computed(() => customersCareStore.popUpItem);
  const company: ComputedRef<string> = computed(() => authStore.company);
  const isLoading: ComputedRef<boolean> = computed(() => authStore.isLoading);
  const customerInfo: ComputedRef<any> = computed(() => customersCareStore?.customerInfo);

  const openDeleteModal = () => {
    customersCareStore.setPopUpItem({
      item: 'logout'
    });
  };

  const handleAccept = async () => {
    authStore.setIsLoading(true);
    if (popUpItem.value?.item) {
      await logout();
      router.push({ name: 'Login' });
      customersCareStore.setPopUpItem(null);
      authStore.setIsLoading(false);
    }
  };

  const closeModal = () => {
    customersCareStore.setPopUpItem(null);
  };
</script>
<style></style>
