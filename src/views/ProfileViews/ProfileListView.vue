<template>
  <div ref="scrollContainer" :class="needPadding ? 'pt-14 lg:pt-0' : 'pt-0'">
    <div v-if="isLoading" class="flex flex-col items-center">
      <PwLoading
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
        class="mt-8"
      ></PwLoading>
      <p class="font-bold mt-4 text-lg text-primary">Actualitzant dades...</p>
    </div>
    <div v-else class="pb-24">
      <!--       <div class="" v-if="listItemsOrdered.length">
        <ActionsCard
          v-for="(item, index) of listItemsOrdered"
          :key="index"
          :item="item"
          :parentKey="parentKey"
          :itemsLenght="listItemsOrdered.length"
          :class="`${item.isDefault ? '' : ''}`"
        >
        </ActionsCard>
      </div> -->
      <WhiteCardComponent class="bg-white dark:bg-dark-background px-4 pt-4 mt-12">
        <ActionsCard
          v-for="(item, index) of listItemsOrdered"
          :key="index"
          :item="item"
          :parentKey="parentKey"
          :itemsLenght="listItemsOrdered.length"
          :class="`${index < listItemsOrdered.length - 1 ? 'border-b border-gray-light dark:border-dark-gray pb-5' : ''}`"
          class="pt-1"
        >
        </ActionsCard>
      </WhiteCardComponent>
      <PwButton
        :text="
          $t('profile.list.createAction', {
            key: routeKey()
          })
        "
        theme="primary-primary-light"
        class="px-2 mt-2 w-full !ml-0 !mr-1.5"
        @click="createItem"
      >
        <font-awesome-icon icon="plus" class="size-[20px] cursor-pointer mx-2" />
      </PwButton>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import type {
    ICustomerInfo,
    ICustomerInfoArrayProperties
  } from '@/stores/customersCare/interfaces/customer-info.interface';
  export default defineComponent({
    name: 'ProfileListView'
  });
</script>
<script setup lang="ts">
  import { onMounted, computed, ref, defineAsyncComponent } from 'vue';
  import type { ComputedRef, Ref } from 'vue';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useRoute, useRouter } from 'vue-router';
  import { PwButton, PwLoading } from 'parlem-webcomponents-common';
  import { useI18n } from 'vue-i18n';
  import { useAuthStore } from '@/stores/auth';
  const ActionsCard = defineAsyncComponent(
    () => import('@/components/ActionsCard/ActionsCard.vue')
  );
  const WhiteCardComponent = defineAsyncComponent(
    () => import('@/components/WhiteCardComponent/WhiteCardComponent.vue')
  );

  const route = useRoute();
  const router = useRouter();
  const { t } = useI18n();
  const authStore: any = useAuthStore();
  const customersCareStore = useCustomersCare();
  const parentKey: Ref<keyof ICustomerInfo> = ref(route.params.key as keyof ICustomerInfo);
  const customerInfo: ComputedRef<ICustomerInfo | null> = computed(
    () => customersCareStore.customerInfo
  );
  const company: ComputedRef<string> = computed(() => authStore.company);
  const isLoading: ComputedRef<string> = computed(() => authStore.isLoading);
  const needPadding: Ref<boolean> = ref(false);
  const scrollContainer = ref<HTMLElement | null>(null);

  onMounted(async () => {
    if (!customerInfo.value) {
      await customersCareStore.getCustomerInfo();
    }
    getScrollPage();
  });

  const getScrollPage = () => {
    if (scrollContainer.value) {
      const windowHeight = window.innerHeight;
      const divHeight = scrollContainer.value.offsetHeight;
      needPadding.value = divHeight >= windowHeight - 90;
    }
  };

  const profileSelectedPropertyInfo: ComputedRef<
    ICustomerInfo[keyof ICustomerInfo][] | ICustomerInfo[keyof ICustomerInfo]
  > = computed(() => customerInfo.value?.[parentKey.value] || []);

  const listItemsOrdered: ComputedRef<ICustomerInfoArrayProperties[]> = computed(() => {
    if (Array.isArray(profileSelectedPropertyInfo.value)) {
      let items = profileSelectedPropertyInfo.value.map((listItem: any, index: number) => {
        return { ...listItem, id: index + 1 };
      });
      return items.sort(
        (firstListItem: any, secondListItem: any) =>
          Number(secondListItem.isDefault) - Number(firstListItem.isDefault)
      );
    }
    return [];
  });

  const createItem = (): void => {
    router.push({ name: 'ProfileCreate', params: { key: parentKey.value } });
  };

  const routeKey = (): string => {
    return t(`profile.${parentKey.value}.title-single`).toLowerCase();
  };
</script>

<style></style>
