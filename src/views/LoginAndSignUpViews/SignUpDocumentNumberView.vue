<template>
  <div class="lg:mt-14 mt-8">
    <div v-if="isLoading" class="flex flex-col items-center">
      <PwLoading
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
        class="mt-8"
      ></PwLoading>
      <p
        class="font-bold mt-4 text-lg"
        :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'"
      >
        {{ $t('login.logging') }}
      </p>
    </div>

    <div v-else class="flex flex-col items-center">
      <div v-if="!isSubmitted" class="w-full">
        <div class="flex flex-col justify-between h-full">
          <div class="flex flex-col justify-center items-center">
            <h3 class="text-4xl font-semibold text-primary mb-4">
              {{ $t('login.identify-yourself') }}
            </h3>
            <h5 class="text-white text-xl mb-16 text-center">
              {{ $t('login.info-signup') }}
            </h5>
          </div>

          <p
            class="font-bold mb-3 w-full"
            :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'"
          >
            {{ $t('login.introduce-document') }}
          </p>
          <PwInputText
            v-model="documentNumber"
            type="text"
            :placeholder="$t('login.dni-example')"
            class="mb-3 w-full"
            :validations="['required', 'dni']"
            @errors="getErrors"
          />
          <PwButton
            :text="$t('login.send')"
            :theme="company === 'Toxo' ? 'secondary-primary-light' : 'primary-primary-light'"
            class="text-lg w-full"
            @click="sendDocument"
            :disabled="!documentNumber || hasErrors"
          />
          <p
            class="text-right text-primary cursor-pointer text-sm underline font-bold"
            @click="router.push('/login')"
          >
            {{ $t('login.already-client') }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, type ComputedRef, type Ref } from 'vue';
  import { PwButton, PwInputText, PwLoading } from 'parlem-webcomponents-common';
  import { useRouter } from 'vue-router';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useAuthStore } from '@/stores/auth/index';
  import type { IAuthStore } from '@/stores/auth/interfaces/store.interface';
  import type { ICustomersCareStore } from '@/stores/customersCare/interfaces/store.interface';

  const customersCareStore: ICustomersCareStore = useCustomersCare();
  const authStore: IAuthStore = useAuthStore();

  const router = useRouter();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const documentNumber: Ref<string> = ref('');
  const isSubmitted: Ref<boolean> = ref(false);
  const isLoading: Ref<boolean> = ref(false);
  const hasErrors: Ref<boolean> = ref(false);

  function getErrors(errors: any[]) {
    hasErrors.value = errors.length > 0;
  }

  const sendDocument = async (): Promise<void> => {
    try {
      isLoading.value = true;
      const response = await customersCareStore.getSendMailDocument(
        documentNumber.value,
        company.value
      );
      if (response?.status >= 400) {
        router.push({ name: 'UserRegisteredView' });
      } else if (response?.data === 'Mail sent') {
        router.push({ name: 'MailSentView' });
      }
    } catch (err) {
      console.error('Error en la solicitud:', err);
    } finally {
      isLoading.value = false;
    }
  };
</script>
