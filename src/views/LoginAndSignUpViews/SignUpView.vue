<template>
  <div
    class="flex flex-col h-full min-h-screen w-screen px-4 lg:px-6 pt-6 lg:pt-10 pb-10 lg:pb-16"
    :class="
      ['Toxo'].includes(company)
        ? 'bg-primary'
        : ['Xartic'].includes(company)
          ? 'bg-xartic'
          : 'bg-secondary'
    "
  >
    <div>
      <div class="w-full flex flex-col justify-center items-center max-w-">
        <img
          :src="logo"
          loading="lazy"
          height="40"
          width="140"
          alt="icon"
          class="cursor-pointer m-3 max-w-[200px]"
        />
      </div>
    </div>
    <div class="flex justify-center w-full">
      <PwStepper
        class="mt-5 max-w-[400px] w-full"
        :theme="company === 'Toxo' ? 'secondary' : 'primary'"
        :initial-step="1"
        :currentstep="currentStep"
        :steps="stepper"
        textClass="text-white"
      />
    </div>

    <div class="flex justify-center h-full p-3">
      <router-view class="max-w-[550px]" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, watch, type ComputedRef } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { PwStepper } from 'parlem-webcomponents-common';
  import { useAuthStore } from '@/stores/auth/index';

  const authStore: any = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const logo = `${import.meta.env.VITE_LOGOS_URL}/${company.value.toLowerCase()}-logo-white.webp`;
  import { useI18n } from 'vue-i18n';
  const { t } = useI18n();
  const router = useRouter();
  const route = useRoute();

  const stepper = [
    { label: '1', description: t('login.identify'), route: '/signup/documentnumber' },
    { label: '2', description: t('login.verification'), route: '/signup/mailsent' },
    { label: '3', description: t('login.signup-step'), route: '/signup/socialemail' }
  ];

  const currentStep = computed(() => {
    const index = stepper.findIndex((step) => step.route === route.path);
    return index !== -1 ? index + 1 : 4;
  });

  watch(
    () => route.path,
    (newPath) => {
      if (newPath === '/signup') {
        router.push('/signup/documentnumber');
      }
    },
    { immediate: true }
  );
</script>
