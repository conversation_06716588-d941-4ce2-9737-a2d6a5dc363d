<template>
  <div class="lg:mt-14 mt-8">
    <div class="w-full">
      <div class="flex flex-col items-center justify-between h-full">
        <h3
          class="text-4xl font-semibold mb-4"
          :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'"
        >
          {{ $t('login.verify') }}
        </h3>
        <h5 class="text-white text-center text-xl">
          {{ $t('login.mail-sent') }} {{ $t('login.wait-email') }}
        </h5>
        <h6 class="text-white text-center text-xl mt-5">
          {{ $t('login.verify-email') }}
        </h6>

        <div class="flex justify-center my-12">
          <font-awesome-icon
            icon="fa-regular fa-paper-plane"
            class="size-[120px] font-bold text-center"
            :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'"
          ></font-awesome-icon>
        </div>
        <div class="flex flex-col items-center w-full justify-center">
          <PwButton
            :theme="
              company === 'Toxo' || company === 'Xartic' ? 'outline-secondary' : 'outline-primary'
            "
            :text="$t('login.send-again')"
            class="text-lg hover:bg-primary-light"
            @click="router.push({ name: 'SignUpDocumentNumber' })"
          >
          </PwButton>
          <p class="text-white mt-5 cursor-pointer text-md hover:text-primary-light">
            {{ $t('login.help') }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PwButton } from 'parlem-webcomponents-common';
  import { computed } from 'vue';
  import type { ComputedRef } from 'vue';
  import { useRouter } from 'vue-router';
  import { useAuthStore } from '@/stores/auth/index';

  const authStore: any = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const router = useRouter();
</script>

<style></style>
