<template>
  <div class="lg:mt-14 mt-8">
    <div class="w-full">
      <div class="flex flex-col items-center justify-between h-full">
        <h3
          class="text-4xl font-semibold mb-4"
          :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'"
        >
          {{ $t('login.we-sorry') }}
        </h3>
        <h5 class="text-white text-center text-xl">
          {{ $t('login.user-registered') }}
        </h5>
        <div class="flex justify-center my-12">
          <font-awesome-icon
            icon="fa-regular fa-face-sad-cry"
            class="size-[140px] font-bold mb-4 text-center"
            :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'"
          ></font-awesome-icon>
        </div>
        <PwButton
          :text="$t('login.try-again')"
          :theme="
            company === 'Xartic' || company === 'Toxo' ? 'outline-secondary' : 'outline-primary'
          "
          class="text-lg hover:bg-primary-light"
          @click="router.push('/signup/documentnumber')"
        />
        <p class="text-white mt-5 cursor-pointer text-md hover:text-primary-light">
          {{ $t('login.help') }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, type ComputedRef } from 'vue';
  import { PwButton } from 'parlem-webcomponents-common';
  import { useRouter } from 'vue-router';
  import { useAuthStore } from '@/stores/auth/index';
  import type { IAuthStore } from '../../stores/auth/interfaces/store.interface';

  const authStore: IAuthStore = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const router = useRouter();
</script>

<style></style>
