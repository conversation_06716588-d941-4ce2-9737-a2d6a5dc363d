<template>
  <div class="lg:mt-14 mt-8">
    <div class="w-full">
      <div class="flex flex-col items-center justify-between h-full">
        <h3
          class="text-4xl font-semibold mb-4"
          :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'"
        >
          {{ $t('login.success-title') }}
        </h3>
        <h5 class="text-white text-center text-xl">
          {{ $t('login.success') }}
        </h5>
        <h6 class="text-white text-center text-xl mt-2">
          {{ $t('login.success-login') }}
        </h6>

        <div class="flex justify-center my-12">
          <font-awesome-icon
            icon="fa-regular fa-face-grin-hearts"
            class="size-[120px] font-bold text-center"
            :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'"
          ></font-awesome-icon>
        </div>
        <div class="flex flex-col items-center w-full justify-center">
          <PwButton
            :theme="company === 'Toxo' ? 'secondary-light' : 'primary-light'"
            :text="$t('login.login')"
            class="text-lg px-6"
            @click="handleLogin"
          >
          </PwButton>
          <p class="text-white mt-5 cursor-pointer text-md hover:text-primary-light">
            {{ $t('login.help') }}
          </p>
          <p
            class="text-right text-primary cursor-pointer text-sm underline font-bold"
            @click="router.push('/login')"
          >
            {{ $t('login.go-login') }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PwButton } from 'parlem-webcomponents-common';
  import { computed } from 'vue';
  import type { ComputedRef } from 'vue';
  import { useRouter } from 'vue-router';
  import { useAuthStore } from '@/stores/auth/index';
  import { login, initialize } from '@/utils/msal-b2c/msalFunctions';

  const authStore: any = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);
  const router = useRouter();

  const handleLogin = async () => {
    await initialize();
    await login();
  };
</script>

<style></style>
