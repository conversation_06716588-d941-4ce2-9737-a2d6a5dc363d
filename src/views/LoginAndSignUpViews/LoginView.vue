<template>
  <!--   <main
    class="flex flex-col justify-between items-center overflow-y-auto min-h-screen h-full px-4 lg:px-6 pt-6 lg:pt-10 pb-10 lg:pb-16"
    :class="
      isAuthRoute
        ? `w-screen relative bg-cover bg-center max-h-screen ${
            company === 'Toxo' ? 'bg-primary' : company === 'Xartic' ? 'bg-xartic' : 'bg-secondary'
          }`
        : 'w-full max-w-[1280px] m-auto'
    "
  > -->
  <main
    class="flex flex-col justify-between items-center h-screen overflow-y-auto min-h-screen h-full px-4 lg:px-6 pt-6 lg:pt-10 pb-10 lg:pb-16"
    :class="
      route.name === 'Login'
        ? `w-screen bg-secondary relative bg-cover bg-center max-h-screen ${
            company === 'Toxo' ? 'bg-primary' : company === 'Xartic' ? 'bg-xartic' : 'bg-secondary'
          }`
        : 'w-full max-w-[1280px] m-auto'
    "
    :style="backgroundClasses()"
  >
    <div class="max-w-[800px]">
      <div class="w-full flex justify-center">
        <img
          :src="logo"
          loading="lazy"
          alt="icon"
          class="cursor-pointer m-3 max-w-[200px]"
          width="140"
          height="40"
        />
      </div>
      <div class="pt-10 h-full">
        <h1 class="lg:text-[86px] sm:text-7xl text-6xl text-white font-semibold">
          {{ $t('login.connect') }}
          <span class="font-bold" :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'">{{
            company
          }}</span>
          {{ $t('login.where') }}
        </h1>
        <!--         <div
          class="lg:flex flex-col p-6 justify-between w-full m-auto max-w-[500px] items-center bg-black/50 h-full rounded-lg"
        >
          <div class="w-full flex justify-center">
            <img :src="logo" loading="lazy" alt="icon" class="cursor-pointer m-3" />
          </div>
          <div class="w-full flex flex-col items-center">
            <PwButton
              :text="'Entrar al meu compte'"
              theme="primary-white"
              class="m-1 max-w-[600px]"
              @click="handleLogin"
            >
            </PwButton>
            <RouterLink class="text-primary font-bold" to="/profile"
              >u compte</RouterLink
            >
          </div>
        </div> -->
      </div>
    </div>

    <div class="w-full max-w-[800px] flex flex-col justify-end items-center lg:h-screen">
      <PwButton
        :text="$t('login.login')"
        :theme="company === 'Toxo' ? 'secondary' : 'primary-primary-light'"
        class="m-1 text-lg"
        @click="handleLogin"
      >
      </PwButton>

      <router-link :to="{ name: 'SignUpDocumentNumber' }" class="w-full text-center">
        <p
          class="hover:text-primary-light text-lg font-bold mt-2"
          :class="company === 'Toxo' ? 'text-secondary' : 'text-primary'"
        >
          {{ $t('login.signup') }}
        </p>
      </router-link>
    </div>
  </main>
</template>

<script setup lang="ts">
  import { PwButton } from 'parlem-webcomponents-common';
  import { useRoute, RouterLink } from 'vue-router';
  import { useAuthStore } from '@/stores/auth/index';
  import { onMounted, ref, computed, type Ref, type ComputedRef } from 'vue';
  import { login, initialize } from '@/utils/msal-b2c/msalFunctions';
  import { updateThemeColor } from '@/utils/colors/updatePwaBackgrounColor';
  import { getCssVaribleValue } from '@/utils/colors/getCssVaribleValue';
  import loginImageParlem from '@/assets/images/login-images/login_image_parlem.avif';
  import loginImageToxo from '@/assets/images/login-images/login_image_toxo.avif';
  import loginImageAproop from '@/assets/images/login-images/login_image_aproop.avif';
  import loginImageXartic from '@/assets/images/login-images/login_image_xartic.avif';

  const route = useRoute();
  const authStore: any = useAuthStore();
  const company: ComputedRef<string> = computed(() => authStore.company);

  const backgroundImage: Ref<{ [key: string]: string }> = ref({
    Parlem: loginImageParlem,
    Toxo: loginImageToxo,
    Aproop: loginImageAproop,
    Xartic: loginImageXartic
  });

  const logo = `${import.meta.env.VITE_LOGOS_URL}/${company.value.toLowerCase()}-logo-white.webp`;

  onMounted(() => {
    updatePwaBackgroundColor();
  });

  const handleLogin = async () => {
    await initialize();
    await login();
  };

  const backgroundClasses = () => {
    if (route.name === 'Login') {
      return {
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6) ), url(${backgroundImage.value[company.value]})`
      };
    }
  };

  const updatePwaBackgroundColor = () => {
    updateThemeColor(`rgb(${getCssVaribleValue('--color-secondary')})`);
  };
</script>
