<template>
  <div class="lg:mt-14 mt-8">
    <div v-if="isLoading" class="flex flex-col items-center">
      <PwLoading
        :company="company"
        loading-style="w-32 h-32"
        loading-image-style="w-[20px]"
        class="mt-8"
      />
      <p
        class="font-bold mt-4 text-lg"
        :class="['Toxo'].includes(company) ? 'text-secondary' : 'text-primary'"
      >
        {{ $t('login.sending') }}
      </p>
    </div>

    <div v-else class="w-full">
      <div class="h-full flex flex-col justify-center items-center">
        <h3
          class="text-4xl font-semibold mb-4"
          :class="['Toxo'].includes(company) ? 'text-secondary' : 'text-primary'"
        >
          {{ $t('login.signup') }}
        </h3>
        <h5 class="text-white text-center text-xl mb-8">
          {{ $t('login.social-email-info') }}
        </h5>

        <div
          v-if="!isFormVisible"
          class="flex flex-col w-full max-w-[380px] items-center mt-6 space-y-4"
        >
          <button
            v-for="provider in providers"
            :key="provider.name"
            class="flex items-center justify-center bg-white/30 shadow-lg py-3 rounded-full w-full cursor-pointer hover:bg-white/45 transition"
            @click="selectProvider(provider)"
          >
            <font-awesome-icon
              :icon="provider.icon"
              class="size-[30px] mr-4"
              :class="
                company === 'Toxo'
                  ? 'text-secondary'
                  : company === 'Xartic'
                    ? 'text-white'
                    : 'text-primary'
              "
            />
            <span class="text-md font-semibold text-white">
              {{ provider.text }}
            </span>
          </button>
        </div>

        <div v-else class="mt-4 w-full">
          <!--           <div class="flex items-center mb-3">
            <font-awesome-icon
              icon="fa-solid fa-chevron-left"
              class="text-primary cursor-pointer size-[15px] mr-2"
              @click="isFormVisible = false"
            />
            <p
              class="text-sm font-semibold text-primary cursor-pointer"
              @click="isFormVisible = false"
            >
              {{ $t('login.go-back') }}
            </p>
          </div> -->

          <p
            class="text-left font-bold mb-3"
            :class="
              company === 'Toxo'
                ? 'text-secondary'
                : company === 'Xartic'
                  ? 'text-white'
                  : 'text-primary'
            "
          >
            {{ $t('login.social-email') }} {{ $t('login.from') }} {{ selectedProvider?.name }}
          </p>
          <PwInputText
            type="text"
            v-model="email"
            :placeholder="$t('login.social-email')"
            class="mb-3 w-full"
            :validations="['required', 'email']"
            @errors="getErrors"
          />

          <p
            class="text-left font-bold mb-3 mt-3"
            :class="
              company === 'Toxo'
                ? 'text-secondary'
                : company === 'Xartic'
                  ? 'text-white'
                  : 'text-primary'
            "
          >
            {{ $t('login.repeat-social-mail') }} de {{ selectedProvider?.name }}
          </p>
          <PwInputText
            type="text"
            v-model="confirmEmail"
            :placeholder="$t('login.repeat-social-mail')"
            class="mb-3 w-full"
            :validations="['required', 'email']"
            @errors="getErrors"
          />

          <PwButton
            :text="$t('login.send-login')"
            :theme="
              ['Toxo'].includes(company) ? 'secondary-primary-light' : 'primary-primary-light'
            "
            class="text-lg mt-3"
            :disabled="!isEmailValid"
            @click="sendMail"
          />

          <p
            v-if="errorMessage"
            class="text-center mt-3"
            :class="['Toxo', 'Xartic'].includes(company) ? 'text-white' : 'text-primary'"
          >
            {{ errorMessage }}
          </p>
          <div class="flex items-center justify-start w-full mt-5 hover:opacity-70">
            <font-awesome-icon
              icon="fa-solid fa-chevron-left"
              class="cursor-pointer size-[15px] mr-2"
              :class="['Toxo', 'Xartic'].includes(company) ? 'text-white' : 'text-primary'"
              @click="isFormVisible = false"
            />
            <p
              class="text-center cursor-pointer text-sm font-bold"
              @click="isFormVisible = false"
              :class="['Toxo', 'Xartic'].includes(company) ? 'text-white' : 'text-primary'"
            >
              {{ $t('login.go-back') }}
            </p>
          </div>
        </div>
      </div>
      <p class="text-center text-white mt-5 cursor-pointer text-md hover:text-primary-light">
        {{ $t('login.help') }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PwButton, PwInputText, PwLoading } from 'parlem-webcomponents-common';
  import { ref, computed, type ComputedRef } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useCustomersCare } from '@/stores/customersCare';
  import { useAuthStore } from '@/stores/auth/index';
  import { useI18n } from 'vue-i18n';

  const authStore: any = useAuthStore();
  const customersCareStore: any = useCustomersCare();
  const route = useRoute();
  const { t } = useI18n();
  const router = useRouter();
  const token = ref(route.query.token || '');
  const company: ComputedRef<string> = computed(() => authStore.company);
  const isLoading = ref(false);
  const hasErrors = ref(false);
  const errorMessage = ref('');
  const email = ref('');
  const confirmEmail = ref('');
  const isFormVisible = ref(false);
  const selectedProvider = ref<{ name: string; icon: string; text: string } | null>(null);

  const providers = [
    { name: 'Facebook', icon: 'fa-brands fa-facebook', text: t('login.continue') + ' Facebook' },
    { name: 'Microsoft', icon: 'fa-brands fa-microsoft', text: t('login.continue') + ' Microsoft' },
    { name: 'Google', icon: 'fa-brands fa-google', text: t('login.continue') + ' Google' }
  ];

  const isEmailValid = computed(() => email.value !== '' && email.value === confirmEmail.value);

  function getErrors(errors: any[]) {
    hasErrors.value = errors.length > 0;
  }

  const selectProvider = (provider: { name: string; icon: string; text: string }) => {
    selectedProvider.value = provider;
    isFormVisible.value = true;
  };

  const sendMail = async () => {
    if (!token.value) {
      console.error('Error');
      errorMessage.value = 'Error.';
      return;
    }

    try {
      isLoading.value = true;
      errorMessage.value = '';

      const response = await customersCareStore.getSaveSocialMail(email.value, token.value);
      if (response?.status === 200) {
        router.push({
          name: 'SuccessSignUp'
        });
      } else {
        errorMessage.value = 'Error.';
      }
    } catch (err) {
      console.error('Error:', err);

      errorMessage.value = 'Error';
    } finally {
      isLoading.value = false;
    }
  };
</script>
