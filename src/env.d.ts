/// <reference types="vite/types/importMeta.d.ts" />

interface ImportMetaEnv {
  readonly VITE_NODE_ENV: string
  readonly VITE_NODE_ENV_MODE: development | production | undefined
  readonly VITE_DEFAULT_LOCALE: string
  readonly VITE_FALLBACK_LOCALE: string
  readonly VITE_BASE_URL: string
  readonly VITE_ASSETS_BASE_URL: string
  readonly VITE_COVERAGE_WC: string
  readonly VITE_VIEWS_URL: string
  readonly VITE_ICONS_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
