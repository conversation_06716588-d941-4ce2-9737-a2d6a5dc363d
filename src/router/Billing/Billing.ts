const AppMainLayoutView = () => import('@/views/AppLayoutViews/AppMainLayoutView.vue');

export default [
  {
    path: '/billing',
    name: 'Billing',
    component: AppMainLayoutView,
    meta: {
      component: 'BillingView',
      title: 'billing.title'
    },
    children: [
      {
        path: 'invoice/:id',
        name: 'Invoice',
        component: AppMainLayoutView,
        meta: {
          component: 'InvoiceView',
          title: 'billing.invoice.download-invoice-title',
          showIdInTitle: true
        }
      },
      {
        path: 'incidence',
        name: 'BillingIncidence',
        component: AppMainLayoutView,
        props: true,
        meta: {
          component: 'IncidenceView',
          title: 'incidence.title'
        }
      }
    ]
  }
];
