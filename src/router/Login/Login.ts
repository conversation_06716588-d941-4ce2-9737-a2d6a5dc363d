import LoginView from '@/views/LoginAndSignUpViews/LoginView.vue';
import SignUpView from '@/views/LoginAndSignUpViews/SignUpView.vue';
import SignUpDocumentNumberView from '@/views/LoginAndSignUpViews/SignUpDocumentNumberView.vue';
import SignUpSocialEmailView from '@/views/LoginAndSignUpViews/SignUpSocialEmailView.vue';
import MailSentView from '@/views/LoginAndSignUpViews/MailSentView.vue';
import UserRegisteredView from '@/views/LoginAndSignUpViews/UserRegisteredView.vue';
import SuccessSignUp from '@/views/LoginAndSignUpViews/SuccessSignUp.vue';

export default [
  {
    path: '/',
    alias: ['/login'],
    name: 'Login',
    component: LoginView,
    meta: {
      isAuthRequired: false
    }
  },
  {
    path: '/signup',
    name: 'SignUp',
    meta: {
      isAuthRequired: false
    },
    component: SignUpView,
    children: [
      {
        path: 'documentnumber',
        name: 'SignUpDocumentNumber',
        component: SignUpDocumentNumberView,
        meta: {
          isAuthRequired: false
        }
      },
      {
        path: 'mailsent',
        name: 'MailSentView',
        component: MailSentView,
        meta: {
          isAuthRequired: false
        }
      },
      {
        path: 'userregistered',
        name: 'UserRegisteredView',
        component: UserRegisteredView,
        meta: {
          isAuthRequired: false
        }
      },
      {
        path: 'socialemail',
        name: 'SignUpSocialEmail',
        component: SignUpSocialEmailView,
        meta: {
          isAuthRequired: false
        }
      },
      {
        path: 'success',
        name: 'SuccessSignUp',
        component: SuccessSignUp,
        meta: {
          isAuthRequired: false
        }
      }
    ]
  }
];
