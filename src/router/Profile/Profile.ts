const AppMainLayoutView = () => import('@/views/AppLayoutViews/AppMainLayoutView.vue');

export default [
  {
    path: '/profile',
    name: 'Profile',
    component: AppMainLayoutView,
    meta: {
      component: 'ProfileView',
      title: 'profile.title'
    },
    children: [
      {
        path: 'list/:key?',
        name: 'ProfileList',
        component: AppMainLayoutView,
        meta: {
          component: 'ProfileListView',
          title: 'profile.list.title'
        }
      },
      {
        path: 'edit/:key?/:id?',
        name: 'ProfileEdit',
        component: AppMainLayoutView,
        meta: {
          component: 'ProfileEditView',
          title: 'profile.edit.title',
          showIdInTitle: true
        }
      },
      {
        path: 'create/:key?/:id?',
        name: 'ProfileCreate',
        component: AppMainLayoutView,
        meta: {
          component: 'ProfileEditView',
          title: 'profile.create.title'
        }
      },
      {
        path: 'incidence',
        name: 'ProfileIncidence',
        component: AppMainLayoutView,
        props: true,
        meta: {
          component: 'IncidenceView',
          title: 'incidence.title'
        }
      }
    ]
  }
];
