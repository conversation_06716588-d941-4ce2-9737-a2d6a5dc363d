import AppIFrameLayoutView from '@/views/AppLayoutViews/AppIFrameLayoutView.vue';

const AppMainLayoutView = () => import('@/views/AppLayoutViews/AppMainLayoutView.vue');

export default [
  {
    path: '/products',
    name: 'Products',
    component: AppMainLayoutView,
    meta: {
      component: 'ProductView',
      title: 'products.title'
    },
    children: [
      {
        path: 'roaming/:id',
        name: 'RoamingView',
        component: AppMainLayoutView,
        meta: {
          component: 'RoamingView',
          title: 'products.roaming.name',
          showIdInTitle: true
        }
      },
      {
        path: 'subscription/:id',
        name: 'AddAddonsView',
        component: AppMainLayoutView,
        meta: {
          component: 'AddAddonsView',
          title: 'products.subscription.name',
          showIdInTitle: true
        }
      },
      {
        path: 'optionals/:id',
        name: 'AddAddonsView',
        component: AppMainLayoutView,
        meta: {
          component: 'AddAddonsView',
          title: 'products.subscription.name',
          showIdInTitle: true
        }
      },
      {
        path: 'rate/:rateCode',
        name: 'RateDetailView',
        component: AppMainLayoutView,
        meta: {
          component: 'RateDetailView',
          title: 'products.rate.name',
          showIdInTitle: true
        }
      },
      {
        path: 'detailcalls/:id',
        name: 'DetailCallsView',
        component: AppMainLayoutView,
        meta: {
          component: 'DetailCallsView',
          title: 'products.call-details.name',
          showIdInTitle: true
        }
      },
      {
        path: 'incidence',
        name: 'ProductsIncidence',
        component: AppMainLayoutView,
        meta: {
          component: 'IncidenceView',
          title: 'incidence.title',
          showIdInTitle: true
        }
      }
    ]
  }
];
