const AppIFrameLayoutView = () => import('@/views/AppLayoutViews/AppIFrameLayoutView.vue');

export default [
  {
    path: '/help-center',
    name: 'Help',
    component: AppIFrameLayoutView,
    meta: {
      component: 'IFrameView',
      title: 'help.title',
      url: {
        Parlem: 'https://parlem.com/ajuda',
        Aproop: 'https://aprooptelecom.com/ajuda',
        Xartic: 'https://xartic.net/contacte/',
        Toxo: 'https://toxotelecom.gal/es/info/preguntas-frecuentes-6'
      }
    }
  },
  {
    path: '/roaming-help-center',
    name: 'RoamingHelpCenter',
    component: AppIFrameLayoutView,
    meta: {
      component: 'IFrameView',
      title: 'products.roaming.title',
      url: {
        Parlem: 'https://parlem.com/tarifes-roaming-internacional',
        Aproop: 'https://aprooptelecom.com/roaming/',
        Xartic: 'https://xartic.net/faqs/',
        Toxo: 'https://toxotelecom.gal/gl/info/roaming-2025'
      }
    }
  }
];
