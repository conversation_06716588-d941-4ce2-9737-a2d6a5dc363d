import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '@/stores/auth/index';
import checkUserCredentials from '@/utils/check-user-credentials-msal';
import LoginRoutes from '@/router/Login/Login';
import HomeRoutes from '@/router/Home/Home';
import ProfileRoutes from '@/router/Profile/Profile';
import BillingRoutes from '@/router/Billing/Billing';
import ProductsRoutes from '@/router/Products/Products';
import NotificationsRoutes from '@/router/Notifications/Notifications';
import HelpRoutes from '@/router/Help/Help';
import PromotionsRoutes from '@/router/Promotions/Promotions';
import ErrorRoutes from '@/router/Error/index';

const router = createRouter({
  history: createWebHistory(),
  routes: [
    ...LoginRoutes,
    ...HomeRoutes,
    ...ProfileRoutes,
    ...BillingRoutes,
    ...ProductsRoutes,
    ...NotificationsRoutes,
    ...HelpRoutes,
    ...PromotionsRoutes,
    ...ErrorRoutes
  ],

  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) return savedPosition;
    else return { left: 0, top: 0 };
  }
});

router.beforeEach(async (to, from, next) => {
  if (to.name === from.name) return next();

  await checkUserCredentials();

  const authStore = useAuthStore();
  const isAuthenticated = authStore.accessToken && !authStore.authenticationError;

  if (isAuthenticated) {
    return [
      'Login',
      'SignUpSocialEmail',
      'UserRegisteredView',
      'SignUpDocumentNumber',
      'MailSentView',
      'SuccessSignUp'
    ].includes(to.name as string)
      ? next({ name: 'Home' })
      : next();
  }

  if (to.meta.isAuthRequired === false && !isAuthenticated) {
    return next();
  }

  if (!isAuthenticated && to.name !== 'NotAuthorized') {
    return next({ name: 'NotAuthorized' });
  }

  return next({ name: 'Login' });
});

export default router;
