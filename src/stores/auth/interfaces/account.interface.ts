export type IAccount = {
  homeAccountId: string
  environment: string
  tenantId: string
  username: string
  localAccountId: string
  name?: string
  idToken?: string
  idTokenClaims?: TokenClaims & {
    [key: string]: string | number | string[] | object | undefined | unknown
  }
  nativeAccountId?: string
  authorityType?: string
  tenantProfiles?: Map<string, TenantProfile>
}
export type TenantProfile = Pick<IAccount, 'tenantId' | 'localAccountId' | 'name'> & {
  isHomeTenant?: boolean
}

export type TokenClaims = {
  aud?: string
  iss?: string
  iat?: number
  nbf?: number
  oid?: string
  sub?: string
  tid?: string
  tfp?: string
  acr?: string
  ver?: string
  upn?: string
  preferred_username?: string
  login_hint?: string
  emails?: string[]
  name?: string
  nonce?: string
  exp?: number
  home_oid?: string
  sid?: string
  cloud_instance_host_name?: string
  cnf?: {
    kid: string
  }
  x5c_ca?: string[]
  ts?: number
  at?: string
  u?: string
  p?: string
  m?: string
  roles?: string[]
  amr?: string[]
  idp?: string
  auth_time?: number
  tenant_region_scope?: string
  tenant_region_sub_scope?: string
}
