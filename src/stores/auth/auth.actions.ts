import type { IAccount } from './interfaces/account.interface';
import type { IAuthStore } from './interfaces/store.interface';

export function setAccessToken(this: IAuthStore, accessToken: string) {
  this.accessToken = accessToken;
}
export function setAccount(this: IAuthStore, account: IAccount) {
  this.account = account;
}
export function setAuthenticationError(this: IAuthStore, authenticationError: boolean) {
  this.authenticationError = authenticationError;
}
export function setIsAuthenticated(this: IAuthStore, isAuthenticated: boolean) {
  this.isAuthenticated = isAuthenticated;
}
export function setCompany(this: IAuthStore, company: string) {
  this.company = company;
}
export async function setIsLoading(this: IAuthStore, isLoading: boolean) {
  this.isLoading = isLoading;
}
