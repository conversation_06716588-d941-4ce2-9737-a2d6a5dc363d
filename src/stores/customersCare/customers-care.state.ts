import type { ICustomersCareState } from './interfaces/state.interface';

export default (): ICustomersCareState => ({
  picklists: [],
  customerInfo: null,
  profileItemToEdit: null,
  roamingService: null,
  popUpItem: false,
  invoicesByNif: {
    status: '',
    invoices: [],
    error: null
  },
  invoicePDF: '',
  services: [],
  products: {},
  consumptions: {},
  numTelInfo: {
    status: 'true',
    consumptions: null,
    error: null
  },
  rateDetail: null,
  addons: [],
  cdrConsumsByNum: [],
  selectedProduct: null,
  selectedService: null
});
