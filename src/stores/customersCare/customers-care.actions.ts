import type { IApiRes } from '@/services/api/interfaces';
import type { ICustomersCareStore } from './interfaces/store.interface';
import { apiCustomersCareService, apiOptimalPrice, apiShoppingService } from '@/services/api';
import { useAuthStore } from '@/stores/auth';
import type { ICustomerInfo } from '@/stores/customersCare/interfaces/customer-info.interface';
import type { IRoamingService, IRoamingStatus } from './interfaces/roaming-service.interface';
import type { IPicklist } from '@/services/api/customersCare/interfaces';
import type { IPrimaryProduct, IProducts } from './interfaces/products.interface';
import type { IConsumption } from '@/stores/customersCare/interfaces/consumption.interface';
import type { IDebt, IDebtItem, IInvoice, IInvoicesItem } from './interfaces/invoice.interface';

function company() {
  const authStore: any = useAuthStore();
  return authStore.company;
}

function setIsLoading(value: boolean) {
  const authStore: any = useAuthStore();
  return authStore.setIsLoading(value);
}

export async function getPicklists(this: ICustomersCareStore) {
  const language: string = this.customerInfo?.preferredLanguage || 'ca';
  const picklists: IPicklist[] = await apiCustomersCareService.getPicklists(language);
  this.picklists = picklists;
}

export async function getCustomerInfo(this: ICustomersCareStore) {
  const customerInfo: ICustomerInfo = await apiCustomersCareService.getCustomerInfo(company());
  Object.keys(customerInfo).forEach((key: string) => {
    if (customerInfo[key] && Array.isArray(customerInfo[key])) {
      customerInfo[key] = this.getCustomerInfoProperty(customerInfo[key]);
    }
  });
  this.customerInfo = customerInfo;
}

export async function updateCustomerInfo<T extends keyof ICustomerInfo>(
  this: ICustomersCareStore,
  property: T,
  newCustomerInfo: ICustomerInfo[T] | ICustomerInfo[T][]
): Promise<number> {
  setIsLoading(true);
  const response: IApiRes<{
    item: ICustomerInfo[T] | ICustomerInfo[T][];
  }> = await apiCustomersCareService.updateCustomerInfo(company(), property, newCustomerInfo);
  if (this.customerInfo && response.data && typeof response.data !== 'string') {
    const item: ICustomerInfo[T][] | ICustomerInfo[T] = response.data.item as
      | ICustomerInfo[T]
      | ICustomerInfo[T][];
    if (Array.isArray(response.data.item as ICustomerInfo[T][])) {
      this.customerInfo[property] = this.getCustomerInfoProperty(item as ICustomerInfo[T][]);
    } else {
      this.customerInfo[property] = item as ICustomerInfo[T];
    }
  }
  setIsLoading(false);
  return response.status;
}

export async function getRoamingService(
  this: ICustomersCareStore,
  company: string,
  subscriptionId: string
): Promise<any> {
  const response: IApiRes<IRoamingService> = await apiCustomersCareService.getRoamingService(
    company,
    subscriptionId
  );
  this.roamingService = response.data;
  return response.data;
}

export async function activeRoamingService(
  this: ICustomersCareStore,
  company: string,
  subscriptionId: string,
  newStatus: boolean
): Promise<any> {
  const response: IApiRes<IRoamingStatus> = await apiCustomersCareService.activeRoamingService(
    company,
    subscriptionId,
    newStatus
  );
  if (this.roamingService && response.status === 200) {
    this.roamingService.isActive = newStatus;
  }
  return response;
}

export function setProfileItemToEdit(this: ICustomersCareStore, item: any | null) {
  this.profileItemToEdit = item;
}

export function getCustomerInfoProperty<T extends keyof ICustomerInfo>(
  this: ICustomersCareStore,
  items: ICustomerInfo[T][]
): ICustomerInfo[T] {
  return items.map((item: ICustomerInfo[T], index: number) => {
    return { ...(item as Object), id: index + 1 };
  }) as ICustomerInfo[T];
}

export async function getServicesByCustomer(
  this: ICustomersCareStore,
  company: string
): Promise<IPrimaryProduct[]> {
  try {
    const response: IApiRes<IPrimaryProduct[]> =
      await apiCustomersCareService.getServicesByCustomer(company);

    if (!response || response.status >= 400 || typeof response.data !== 'object') {
      this.services = null;
      return [];
    }
    const servicesData = response.data;
    const order = ['fiber', 'landline', 'mobile', 'tv'];

    servicesData.sort((serviceA, serviceB) => {
      const indexA = order.indexOf(serviceA.provisioningClass.toLowerCase());
      const indexB = order.indexOf(serviceB.provisioningClass.toLowerCase());

      // If type is not in the order array, assign a large number to push it to the end
      const posA = indexA === -1 ? Number.MAX_SAFE_INTEGER : indexA;
      const posB = indexB === -1 ? Number.MAX_SAFE_INTEGER : indexB;

      return posA - posB;
    });

    this.services = servicesData;
    return this.services;
  } catch (error: any) {
    this.services = [];
    return [];
  }
}

export async function getPrimaryProductsByContractProductId(
  this: ICustomersCareStore,
  company: string,
  contractProductId: string
): Promise<IProducts> {
  try {
    const response: IApiRes<IPrimaryProduct[]> =
      await apiCustomersCareService.getPrimaryProductsByContractProductId(
        company,
        contractProductId
      );

    if (!response || response.status >= 400 || typeof response.data !== 'object') {
      this.products = { ...this.products, [contractProductId]: [] };
      return { ...this.products, [contractProductId]: [] };
    }

    this.products = { ...this.products, [contractProductId]: response.data };
    return { ...this.products, [contractProductId]: response.data };
  } catch (error: any) {
    this.products = { ...this.products, [contractProductId]: [] };
    return { ...this.products, [contractProductId]: [] };
  }
}

export function setSelectedService(this: ICustomersCareStore, service: IPrimaryProduct | null) {
  this.selectedService = service;
}

export function setSelectedProduct(this: ICustomersCareStore, product: IPrimaryProduct | null) {
  this.selectedProduct = product;
}

// _______PopUp_______
export function setPopUpItem(
  this: ICustomersCareStore,
  item: {
    item: any;
  } | null
): void {
  this.popUpItem = item;
}

// Optimal Price

export async function getInvoiceByInvoiceNumber(
  this: ICustomersCareStore,
  invoiceNumber: string
): Promise<string> {
  const response: any = await apiOptimalPrice.getInvoiceByInvoiceNumber(invoiceNumber);
  this.invoicePDF = response;
  return response;
}

export async function getInvoicesByNif(this: ICustomersCareStore, nif: string): Promise<void> {
  const response: IInvoicesItem = await apiOptimalPrice.getInvoicesByNif(nif);
  this.invoicesByNif = response;
}
export async function getDebtByNif(this: ICustomersCareStore, nif: string): Promise<void> {
  const response: IDebtItem = await apiOptimalPrice.getDebtByNif(nif);
  if (response.response) {
    const newJson = `[${response.response?.replace(/'/g, '"')}]`;
    const debtByNif: IDebt[] = JSON.parse(newJson);
    if (debtByNif.length) {
      debtByNif.forEach((debt: IDebt) => {
        const selectedInvoice = this.invoicesByNif?.invoices.find(
          (invoice: IInvoice) => invoice.numFactura === debt.factura.numFactura
        );
        selectedInvoice ? (selectedInvoice.debt = debt) : null;
      });
    }
  }
}

export async function getConsumptionByNumTelephone(
  this: ICustomersCareStore,
  consumptionPayload: {
    numTel: string;
    suscriptionId: string;
    company: string;
  }
): Promise<void> {
  const response: IConsumption[] =
    await apiOptimalPrice.getConsumptionByNumTelephone(consumptionPayload);
  this.consumptions = { ...this.consumptions, [consumptionPayload.numTel]: response };
}

// CATALOG
export async function retrieveRateByCode(this: ICustomersCareStore, code: string): Promise<void> {
  const response: any = await apiShoppingService.retrieveRateByCode(code);
  this.rateDetail = response;
}

export async function retrieveAddonsRatesByRateCode(
  this: ICustomersCareStore,
  rateCode: string,
  productCode: string
): Promise<void> {
  console.log(rateCode, productCode);
  const addons: any = await apiShoppingService.retrieveAddonsRatesByRateCode(rateCode, productCode);
  console.log(addons);
  this.addons = /* addons */ [
    {
      id: 'a0k68000000wk3gAAA',
      company: 'Parlem',
      trademark: 'Parlem',
      tags: [],
      display: {
        externalName: 'IP FIXA',
        pimBusinessKey: 'RP-0107',
        segment: 'Particular'
      },
      finalPrice: {
        priceWithVATFinal: 17,
        priceWithoutVATFinal: 14.04
      },
      offerPrice: {
        durationDaysPromotionalPrize: 0
      },
      payments: {},
      specifications: {
        permanenceComment: 'Permanència associada a la Fibra',
        permanenceTime: '12 mesos',
        permanence: true
      },
      pack: {
        id: 'a055I000000KxWRQA0',
        display: {
          externalName: 'IP FIXA',
          pimBusinessKey: 'PAK0085',
          segment: 'Particular_and_business'
        },
        products: [
          {
            id: 'a0U5I000000gblaUAA',
            display: {
              pimBusinessKey: 'IP-0095',
              segment: 'Particular_and_business'
            },
            cardImage: 'Image-01',
            specifications: {
              provisioningClass: 'Ip',
              provisioningSubClass: 'IPFix',
              provisioningType: 'AddOn'
            }
          }
        ]
      },
      charges: []
    }
  ];
}

export async function getRetrieveSubscriptionCDRConsumsByNumber(
  this: ICustomersCareStore,
  company: string,
  number: number
): Promise<any> {
  const response: IApiRes<IPrimaryProduct> =
    await apiCustomersCareService.getRetrieveSubscriptionCDRConsumsByNumber(company, number);
  this.cdrConsumsByNum = response.status >= 300 ? [] : response.data;
  return response.status >= 300 ? [] : response.data;
}

export async function getSendMailDocument(
  this: ICustomersCareStore,
  documentNumber: string,
  company: string
): Promise<any> {
  const response: IApiRes<IPrimaryProduct> = await apiCustomersCareService.sendMailToCustomer(
    documentNumber,
    company
  );
  return response;
}

export async function getSaveSocialMail(
  this: ICustomersCareStore,
  mail: string,
  token: string
): Promise<any> {
  const response: IApiRes<IPrimaryProduct> = await apiCustomersCareService.saveCustomerSocialMail(
    mail,
    token
  );
  return response;
}
