export interface ICustomerInfo {
  advertisingConfigurations: IAdvertisingConfiguration[];
  shippingAddresses: IAddress[];
  billingAddresses: IAddress[];
  provisionContacts: IProvisionContact[];
  personalData: IPersonalData;
  companyStructure?: ICompanyStructure;
  billingInfos: IBillingInfo[];
  preferredLanguage: string;
  [key: string]: any;
}

export interface ICustomerInfoArrayProperties
  extends IAddress,
    IProvisionContact,
    IBillingInfo,
    IAdvertisingConfiguration,
    ICustomerInfo {
  id?: number;
}

export interface ICompanyStructure {
  administratorDocumentNumber: string;
  administratorDocumentType: string;
  companyManagerFirstName: string;
  companyManagerLastName: string;
}

export interface IAdvertisingConfiguration {
  id?: number;
  company: string;
  messageType: string;
  accepted: boolean;
  acceptanceDate: Date;
  declinationDate: Date;
  info: string | null;
}

export interface IAddress {
  id?: number;
  gescal37: string | null;
  gescal17: string | null;
  country: string;
  state: string | null;
  province: string;
  city: string;
  streetType: string;
  street: string;
  number: string;
  specification: string;
  zip: string;
  isDefault: boolean;
}

export interface IBillingInfo {
  id?: number;
  name: string;
  cccOwner: string;
  cccOwnerIdentification: string;
  iban: string;
  sendBill: string;
  isDefault: boolean;
}

export interface IPersonalData {
  gender: string;
  firstName: string;
  lastName: string;
  documentType: string;
  documentNumber: string;
  nationality: string;
  completeName: string;
  foundationDate?: Date;
  personBirthdate?: Date;
  customerType: string;
  category?: string;
}

export interface IProvisionContact {
  id?: number;
  name: string;
  email: string;
  phone: string;
  isDefault: boolean;
}
