import type { IPicklist } from '@/services/api/customersCare/interfaces';
import type { ICustomerInfo } from './customer-info.interface';
import type { IRoamingService } from './roaming-service.interface';
import type { IInvoicesItem } from '@/stores/customersCare/interfaces/invoice.interface';
import type { IConsumptions } from '@/stores/customersCare/interfaces/consumption.interface';
import type { IPrimaryProduct, IProducts } from './products.interface';

export interface ICustomersCareState {
  picklists: IPicklist[];
  customerInfo: ICustomerInfo | null;
  profileItemToEdit: any | null;
  roamingService: IRoamingService | null;
  popUpItem: any | null;
  invoicesByNif: IInvoicesItem | null;
  invoicePDF: string;
  services: IPrimaryProduct[] | null;
  products: IProducts;
  consumptions: IConsumptions;
  numTelInfo: any;
  rateDetail: any;
  addons: any;
  cdrConsumsByNum: any;
  selectedProduct: null | IPrimaryProduct;
  selectedService: null | IPrimaryProduct;
}
