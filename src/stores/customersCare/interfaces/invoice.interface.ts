export interface IInvoice {
  numFactura: string;
  fecEmision: string;
  fecVencimiento: string;
  enviadaCobros: string;
  totFactura: string;
  debt?: IDebt;
}

export interface IInvoicesItem {
  status: string;
  invoices: IInvoice[];
  error: string | null;
}

export interface IDebt {
  id: number;
  fecEfectividad: number;
  fecAntiguedad: number;
  fecVencimiento: number;
  codConcepto: number;
  factura: {
    id: number;
    numFactura: string;
    desactivarAmortizacion: false;
  };
  codAbonado: number;
  codCliente: number;
  codIncidencia: number;
  impSaldo: number;
  impHaber: number;
  impDebe: number;
  tipDocum: number;
  indFormapago: number;
  txtTitular: string;
  txtNumformatopago: string;
}

export interface IDebtItem {
  status: string;
  response: string;
  error: string | null;
}
