export interface IRate {
  pimBusinessKey: string;
  trademark: string;
  display: IDisplay;
  tags: string[];
  pack: IPack;
  priority: number;
  charges: ICharge[];
  offerPrice: IOfferPrice;
  optionalRates: IOptionalRates[];
  addOnRates: IOptionalRates[];
  rowVersion: Date;
  sellMode: string;
}

export interface IOptionalRates {
  rateChildId: number | string;
  rateChild: IRate;
  productId?: number | string;
  rateId: number | string;
}

export interface ICharge {
  pimBusinessKey: string;
  penaltyPricewithVAT: number;
  penaltyPricewithoutVAT: number;
  chargeSubType: string;
  chargeType: string;
  permanenceMonths: number;
  notProrated: boolean;
  rowVersion: Date;
  channels: string[];
  notations?: string;
}

export interface IDisplay {
  name: string;
  externalName: string;
}

export interface IOfferPrice {
  priceWithVATFinal: number;
  priceWithVATOrigin: number;
  priceWithoutVATFinal: number;
  priceWithoutVATOrigin: number;
  excludeMiniCycleFromPromotionalPrize: boolean;
}

export interface IPack {
  pimBusinessKey: string;
  tier: string;
  display: IDisplay;
  rgu: number;
  packType: string;
  products: IProduct[];
  mbpsSpeed: number;
  gbsData: number;
  minutesCall: number;
  minutesCallFixToFixNational: number;
  minutesCallFixToMobileNational: number;
  rowVersion: Date;
}

export interface IProduct {
  [key: string]: any;
  pimBusinessKey: string;
  display: IDisplay;
  provisioningClass: string;
  provisioningSubClass: string;
  provisioningType: string;
  isOneShotBilling: boolean;
  isSelfRenewing: boolean;
  cardImage: string;
  landLineSpecification?: ILandLineSpecification;
  rowVersion: Date;
  fiberSpecification?: IFiberSpecification;
  refundSpecification?: IRefundSpecification;
  mobileSpecification?: IMobileSpecification;
}

export interface IFiberSpecification {
  coverageType: string;
  isSymmetric: boolean;
  mbpsSpeed: number;
  technologyType: string;
}

export interface ILandLineSpecification {
  minutesCallFixToFixNational: number;
  minutesCallFixToMobileNational: number;
}

export interface IMobileSpecification {
  gbsData: number;
  minutesCall: number;
  technologyType: string;
}

export interface IRefundSpecification {
  gbsData: number;
  minutesCallToMobileNational: number;
  minutesCallToFixNational: number;
  maxSharedMobiles: number;
}
