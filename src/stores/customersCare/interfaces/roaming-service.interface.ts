export interface IRoamingService {
  name: string;
  number: string;
  subscriptionId: null | string;
  serviceId: string;
  isActive: boolean;
  params: {
    additionalProp1?: string;
    additionalProp2?: string;
    additionalProp3?: string;
  };
}
export interface IRoamingStatus {
  company: string;
  customerId: string;
  contractProductId: string;
  status: boolean;
  provider: string;
  contractedSubscriptionId: string;
}
