export interface IPrimaryProduct {
  id: string;
  state?: string;
  number: string | null;
  productCode: string;
  productName: string;
  rootProduct?: string;
  contractedSubscriptionId?: string;
  simShippingDate?: string;
  rateCode: string;
  rateName?: string;
  ratePackType?: string;
  digitalServiceAccesCode?: string;
  fibSpeed?: string;
  mbData?: string;
  fiber: {
    coverageType: string;
    installationAddress: string | null;
    number: string;
    provider: string;
    territoryOwner: string | null;
    specifications: {
      mbpsSpeed: string;
      tag: string | null;
    };
  } | null;
  mobile: {
    specifications: {
      gbsDataInternational: number;
      gbsDataNational: number;
      minutesCallToFixInternational: number;
      minutesCallToFixNational: number;
      minutesCallInternational: number;
      minutesCallNational: number;
      smsInternational: number;
      smsNational: number;
    };
    iccParlem: string;
    numberInfo: { number: string; additionalInfo: string };
    portability: { currentPaymentMethod: string; currentICC: string; donorOperatorCode: string };
    simShippingDate: string;
  } | null;
  tv: object | null;
  landLine: {
    specifications: {
      minutesCallToFixInternational: number;
      minutesCallToFixNational: number;
      minutesCallToMobileInternational: number;
      minutesCallToMobileNational: number;
      smsInternational: number;
      smsNational: number;
    };
  } | null;
  refund: {
    gbsData: number;
    maxSharedMobiles: number;
    minutesCallToFixNational: number;
    minutesCallToMobileNational: number;
  } | null;
  provisioningClass: string;
  provisioningSubClass: string;
  activationDate: string;
}

export interface IProducts {
  [key: string]: IPrimaryProduct[];
}
