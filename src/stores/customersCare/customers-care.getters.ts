import type {
  IConsumption,
  IConsumptions
} from '@/stores/customersCare/interfaces/consumption.interface';
import type { IPrimaryProduct, IProducts } from './interfaces/products.interface';
import type { ICustomersCareState } from './interfaces/state.interface';

export function getLanguage(state: ICustomersCareState): string {
  return state.customerInfo?.preferredLanguage || 'ca';
}

export function getServices(state: ICustomersCareState): IPrimaryProduct[] | null {
  return state.services;
}

export function getProducts(state: ICustomersCareState): IProducts {
  if (state.selectedService?.id) {
    return state.products || {};
  }
  return {};
}
export function getSelectedServiceProducts(state: ICustomersCareState): IPrimaryProduct[] {
  if (state.selectedService?.id) {
    return state.products[state.selectedService?.id] || [];
  }
  return [];
}

export function getMainProduct(state: ICustomersCareState): IPrimaryProduct | null {
  if (state.selectedService?.id) {
    return (
      state.products[state.selectedService?.id]?.find(
        (product: IPrimaryProduct) => product.id === state.selectedService?.id
      ) || null
    );
  }
  return null;
}

export function getAllConsumptions(state: ICustomersCareState): IConsumptions {
  return state.consumptions;
}

export function getPhoneConsumptions(state: ICustomersCareState): IConsumption[] {
  if (state.selectedService?.id && state.selectedService?.number) {
    return (
      state.consumptions[state.selectedService?.number]?.sort(
        (a, b) => a.anoConsumo - b.anoConsumo || a.mesConsumo - b.mesConsumo
      ) || []
    );
  }
  return [];
}
