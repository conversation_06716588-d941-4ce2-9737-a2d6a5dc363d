import { defineStore } from 'pinia'
import state from '@/stores/customersCare/customers-care.state'
import * as actions from '@/stores/customersCare/customers-care.actions'
import * as getters from '@/stores/customersCare/customers-care.getters'
import type { ICustomersCareState } from './interfaces/state.interface'

export const useCustomersCare = defineStore('customersCare', {
  state: (): ICustomersCareState => state(),
  actions: { ...actions },
  getters: { ...getters }
})
