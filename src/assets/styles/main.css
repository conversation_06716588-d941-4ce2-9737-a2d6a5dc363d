@import './base.css';
@import './_fonts.css';
@import './_fontFace.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: 252 189 41;
  --color-secondary: 0 0 0;
  --color-primary-light: 255 242 210;
  --color-primary-medium: 255 233 165;
  /*   --color-primary: #fcbd29;
  --color-primary-medium:#FFE9A5
  --color-primary-light: #fff2d2;
  --color-secondary: #000;
  
 */
}

body {
  width: 100%;
  min-height: 100vh;
  height: 100%;
  background-color: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  font-family: 'Ralew<PERSON>', 'Roboto', 'Helvetica Neue', sans-serif;
  font-feature-settings: 'lnum' 1;
  overflow-x: hidden;
}

#app {
  width: 100vw;
  min-height: 100vh;
}

/* Page transitions style */
/* .fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
} */

a {
  transition: 0.4s;
}

/* @media (hover: hover) {
  a:hover {
    color: rgb(var(--color-primary));
    font-weight: bold;
  }
} */

/* @media (min-width: 1024px) {
    body {
    display: flex;
    place-items: center;
  }
} */

::-webkit-scrollbar {
  width: 8px; /* Amplada de la barra de desplaçament */
  height: 4px; /* Amplada per desplaçament horitzontal */
}

::-webkit-scrollbar-track {
  background: var(--color-background); /* Color de fons */
}

::-webkit-scrollbar-thumb {
  background: var(--color-scroll-bar); /* Color del "thumb" */
  border-radius: 4px; /* Rodona */
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-scroll-bar-hover); /* Color quan passes el ratolí per sobre */
}
