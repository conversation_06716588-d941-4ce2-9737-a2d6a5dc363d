import { fileURLToPath, URL } from 'node:url';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueDevTools from 'vite-plugin-vue-devtools';
import { resolve, dirname } from 'node:path';
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite';
import { VitePWA } from 'vite-plugin-pwa';
import { readFileSync } from 'fs';
import tailwindcss from 'tailwindcss';

// https://vitejs.dev/config/
export default ({ mode }: any) => {
  process.env = {
    ...process.env,
    ...loadEnv(mode, process.cwd())
  };

  return defineConfig({
    mode: JSON.stringify(process.env.VITE_NODE_ENV),
    build: {
      chunkSizeWarningLimit: 1600,
      rollupOptions: {
        output: {
          manualChunks(id: any) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString();
            }
          }
        }
      }
    },
    /*     define: {
      'process.env.NODE_ENV': JSON.stringify(process.env.VITE_NODE_ENV)
    }, */
    plugins: [
      vue(),
      vueDevTools(),
      VueI18nPlugin({
        runtimeOnly: false,
        include: resolve(dirname(fileURLToPath(import.meta.url)), './src/i18n/locales/**')
      }),
      VitePWA({
        injectRegister: 'auto',
        registerType: 'autoUpdate',
        includeAssets: ['favicon.svg', 'offline.html'],
        manifest: JSON.parse(
          readFileSync(
            resolve(__dirname, 'public', process.env.VITE_MANIFEST_PATH || 'manifest-parlem.json'),
            'utf-8'
          )
        ),
        devOptions: {
          enabled: true
        },
        workbox: {
          globPatterns: ['**/*.{js,ts,vue,css,html,ico,png,svg,jpg,jpeg,woff,woff2,ttf,eot}'],
          cleanupOutdatedCaches: true,
          maximumFileSizeToCacheInBytes: 4 * 1024 * 1024,
          runtimeCaching: [
            // Cache per a recursos estàtics (imatges, fonts, icones, etc.)
            {
              urlPattern: /\.(?:png|jpg|jpeg|svg|ico|woff2?)$/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'static-assets-cache',
                expiration: {
                  maxEntries: 100, // Màxim 100 recursos
                  maxAgeSeconds: 60 * 60 * 24 * 30 // 30 dies
                },
                cacheableResponse: {
                  statuses: [0, 200]
                }
              }
            },
            // Cache per a recursos dinàmics (API)
            {
              urlPattern: /^https?:\/\/.*\/(api|json)/, // URLs d'API o JSON
              handler: 'NetworkFirst', // Prioritza la xarxa, amb fallback al cache
              options: {
                cacheName: 'dynamic-api-cache',
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 60 * 60 * 24 // 1 dia
                },
                cacheableResponse: {
                  statuses: [0, 200]
                }
              }
            },

            // Cache per a la pàgina offline
            {
              urlPattern: /\/offline\.html$/,
              handler: 'CacheFirst',
              options: {
                cacheName: 'offline-cache'
              }
            },
            {
              urlPattern: /.*\.worker\.js$/,
              handler: 'CacheFirst',
              options: {
                cacheName: 'workers-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 30 * 24 * 60 * 60 // 30 dies
                }
              }
            }
          ]
        }
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '~': fileURLToPath(new URL('./node_modules', import.meta.url))
      }
    },
    css: {
      postcss: {
        plugins: [tailwindcss()]
      }
    }
  });
};
