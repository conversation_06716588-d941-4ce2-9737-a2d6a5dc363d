variables:
  azureSubscription: AzureSubscription
  version: v$(Build.BuildId)
  isMain: $[eq(variables['Build.SourceBranch'], 'refs/heads/master')]
trigger:
  - develop
  - master
pool:
  vmImage: ubuntu-latest
name: v$(Build.BuildId)
stages:
  - stage: Artifacts
    displayName: Build artifacts
    jobs:
      - job: 'BuildArtifact'
        steps:
          # npm
          - task: NodeTool@0
            inputs:
              versionSpec: '20.18.0'
            displayName: 'Build'
          - script: |
              npm install -g pnpm
              npx update-browserslist-db@latest
              npm install
              npm run build
            displayName: 'npm install and build'
          - task: CopyFiles@1
            displayName: 'Copy Files to: $(Build.ArtifactStagingDirectory)'
            inputs:
              SourceFolder: ./dist
              TargetFolder: '$(Build.ArtifactStagingDirectory)/customerarea/dist'
            condition: and(succeeded(), eq(variables.isMain, true))
          - task: PublishPipelineArtifact@1
            displayName: 'Publish Aritifact'
            inputs:
              targetPath: $(Build.ArtifactStagingDirectory)/customerarea/dist
              artifactName: customerarea
            condition: and(succeeded(), eq(variables.isMain, true))
