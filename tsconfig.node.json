{"extends": "@tsconfig/node20/tsconfig.json", "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*"], "compilerOptions": {"composite": true, "noEmit": true, "target": "ES2022", "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node", "vite/client"]}}