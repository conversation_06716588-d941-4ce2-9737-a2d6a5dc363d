{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.ts", "src/**/*.vue", "src/**/*.d.ts", "src/i18n/locales/en.json", "src/i18n/locales/es.json", "src/i18n/locales/ca.json", "node_modules/vite-plugin-pwa/client.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}}