<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
    <title>Grup Parlem App</title>
    <script>
      if (window.trustedTypes && window.trustedTypes.createPolicy) {
        window.trustedTypes.createPolicy('default', {
          createHTML: (string) => string,
          createScriptURL: (string) => string,
          createScript: (string) => string
        });
      }
    </script>
    <script>
      if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
        window.location.replace(
          'https://' + window.location.host + window.location.pathname + window.location.search
        );
      }
    </script>
  </head>
  <body>
    <div id="loading-screen">
      <div class="logo"></div>
    </div>

    <div id="app" style="display: none"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
