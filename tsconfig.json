{"files": [], "compilerOptions": {"target": "ESNext", "module": "ESNext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "resolveJsonModule": true, "lib": ["ESNext"]}, "lib": ["ESNext", "DOM", "WebWorker"], "references": [{"path": "./tsconfig.node.json"}, {"path": "./tsconfig.app.json"}]}