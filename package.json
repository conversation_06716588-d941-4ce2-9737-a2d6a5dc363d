{"name": "parlem-customerarea-front", "version": "0.0.82", "private": true, "type": "module", "scripts": {"dev": "vite --host localhost --port 8080 --mode parlem", "dev:parlem": "vite --host localhost --port 8091 --mode parlem", "dev:aproop": "vite --host localhost --port 8092 --mode aproop", "dev:xartic": "vite --host localhost --port 8093 --mode xartic", "dev:toxo": "vite --host localhost --port 8094 --mode toxo", "build": "run-p type-check build:prod", "preview": "vite preview", "build:dev": "vite build --mode dev", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode prod", "build:parlem": "vite build --mode parlem", "build:aproop": "vite build --mode aproop", "build:xartic": "vite build --mode xartic", "build:toxo": "vite build --mode toxo", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.7.4", "chart.js": "^4.4.7", "parlem-webcomponents-common": "^1.1.247", "pdfjs-dist": "4.10.38", "pinia": "^2.1.7", "vue": "^3.4.29", "vue-chartjs": "^5.3.2", "vue-gtag": "2.0.1", "vue-i18n": "^9.14.0", "vue-router": "^4.3.3", "workbox-window": "^7.3.0"}, "devDependencies": {"@azure/core-http": "^3.0.4", "@azure/msal-browser": "^3.21.0", "@azure/storage-blob": "^12.24.0", "@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/node": "^20.14.5", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "npm-run-all2": "^6.2.0", "postcss": "^8.4.32", "prettier": "^3.2.5", "tailwindcss": "^3.4.10", "typescript": "^5.5.4", "vite": "^5.3.1", "vite-plugin-pwa": "^0.20.5", "vite-plugin-vue-devtools": "^7.3.1", "vue-tsc": "^2.0.21"}}