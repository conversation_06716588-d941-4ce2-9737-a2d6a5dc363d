/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './public/**/*.html',
    './src/**/*.{vue,js,ts,jsx,tsx,html}',
    './node_modules/parlem-webcomponents-common/**/*.{vue,js,css,ts,jsx,tsx,html}'
  ],
  darkMode: 'class', // or 'media' or 'class'
  theme: {
    extend: {
      colors: {
        primary: 'rgb(var(--color-primary) / <alpha-value>)',
        secondary: 'rgb(var(--color-secondary) / <alpha-value>)',
        'primary-medium': 'rgb(var(--color-primary-medium) / <alpha-value>)',
        'primary-light': 'rgb(var(--color-primary-light) / <alpha-value>)',
        'background-color': 'var(--color-background)'
      }
    },
    screens: {
      xs: '320px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px'
    },
    colors: {
      'gray-light': '#E1E1E1',
      white: '#ffffff',
      black: '#000000',
      error: '#9B1C1C',
      'error-light': '#FFCCCC',
      blue: '#1E90FF',
      'blue-light': '#D0EBFF',
      warning: '#C27803',
      'warning-light': '#FFEA8A',
      success: '#0E9F6E',
      'success-light': '#CCE8D9',
      info: '#6B7280',
      gray: '#c1c1c1',
      'light-background': '#F6f6f6',
      //Dark colors
      dark: '#181818',
      'dark-background': '#282c2d', //'#232526',
      'dark-light': '#282828',
      'dark-hover': '#3c4043',
      'dark-gray-light': '#5B5F62',
      'dark-gray': '#3c4043',
      'gray-dark': '#919191',
      xartic: '#04052e' //'#04052e' //'#140152' //'#240046' //'#17153B' //'#2A004E'
    }
  },
  variants: {
    extend: { textOpacity: ['dark'] }
  },
  plugins: []
};
